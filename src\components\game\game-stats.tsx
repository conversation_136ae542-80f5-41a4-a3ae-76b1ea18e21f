'use client';

import { useState } from 'react';
import type { Direction, ChatMessage } from '@/lib/types';

// Removed laser suggestion functionality - now using tool-based mission system
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowUp, ArrowDown, ArrowLeft, ArrowRight, Zap, RefreshCw, Bot, AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ChatBox } from './chat-box';
import { ScrollArea } from '../ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface GameStatsProps {
  score: number;
  batteries: number;
  onMove: (direction: Direction) => void;
  onFire: (direction: Direction) => void;
  onReset: () => void;
  gameOver: boolean;
  chatHistory: ChatMessage[];
  onChatSubmit: (message: string) => void;
  isChatProcessing: boolean;
  commandLogs: string[];
  isBotOnMission: boolean;
  mission: string | null;
  lastCommandDebugInfo: object | null;
  workflowLog: Array<{
    timestamp: string;
    toolName: string;
    args: any;
    result: any;
    reasoning: string;
  }>;
  showWorkflowLog: boolean;
  onToggleWorkflowLog: () => void;
}

export function GameStats({
  score,
  batteries,
  onMove,
  onFire,
  onReset,
  gameOver,
  chatHistory,
  onChatSubmit,
  isChatProcessing,
  commandLogs,
  isBotOnMission,
  mission,
  lastCommandDebugInfo,
  workflowLog,
  showWorkflowLog,
  onToggleWorkflowLog,
}: GameStatsProps) {
  const [laserDirection, setLaserDirection] = useState<Direction>('up');
  const isBusy = gameOver || isBotOnMission;

  return (
    <Card className="flex flex-col">
      <CardHeader>
        <CardTitle className="font-headline">Game Status</CardTitle>
        <CardDescription>Control your bot and watch your score.</CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col gap-4">
        <div className="flex justify-around text-center">
          <div>
            <p className="text-2xl font-bold">{score}</p>
            <p className="text-sm text-muted-foreground">Score</p>
          </div>
          <div>
            <p className="text-2xl font-bold">{batteries} 🔋</p>
            <p className="text-sm text-muted-foreground">Batteries</p>
          </div>
        </div>
        
        {gameOver && (
            <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Game Over!</AlertTitle>
                <AlertDescription>
                    You were defeated. Reset the game to play again.
                </AlertDescription>
            </Alert>
        )}

        {isBotOnMission && mission && (
            <Alert>
                <Bot className="h-4 w-4 animate-pulse" />
                <AlertTitle>Mission in Progress</AlertTitle>
                <AlertDescription>
                    Objective: {mission}
                </AlertDescription>
            </Alert>
        )}

        <Separator />
        
        <ChatBox 
            messages={chatHistory} 
            onSendMessage={onChatSubmit}
            disabled={isBusy}
            isProcessing={isChatProcessing}
        />
        
        <Separator />

        <div className="space-y-2">
            <h3 className="font-semibold font-headline">Controls</h3>
            <div className="grid grid-cols-3 gap-2 justify-items-center">
                <div></div>
                <Button aria-label="Move Up" size="icon" onClick={() => onMove('up')} disabled={isBusy}><ArrowUp /></Button>
                <div></div>
                <Button aria-label="Move Left" size="icon" onClick={() => onMove('left')} disabled={isBusy}><ArrowLeft /></Button>
                <Button aria-label="Move Down" size="icon" onClick={() => onMove('down')} disabled={isBusy}><ArrowDown /></Button>
                <Button aria-label="Move Right" size="icon" onClick={() => onMove('right')} disabled={isBusy}><ArrowRight /></Button>
            </div>
        </div>
        
        <Separator />

        <div className="space-y-2">
            <h3 className="font-semibold font-headline">Laser</h3>
            <div className="flex gap-2">
                <Select
                    defaultValue="up"
                    onValueChange={(v) => setLaserDirection(v as Direction)}
                    disabled={isBusy}
                >
                    <SelectTrigger>
                        <SelectValue placeholder="Direction" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="up">Up</SelectItem>
                        <SelectItem value="down">Down</SelectItem>
                        <SelectItem value="left">Left</SelectItem>
                        <SelectItem value="right">Right</SelectItem>
                    </SelectContent>
                </Select>
                <Button onClick={() => onFire(laserDirection)} disabled={isBusy || batteries <= 0} className="flex-grow bg-accent hover:bg-accent/90">
                    <Zap className="mr-2 h-4 w-4" /> Fire
                </Button>
            </div>
        </div>

        <Separator />
        
        {/* Removed AI Mode section - now always using deterministic approach */}
        
        {commandLogs.length > 0 && (
            <>
                <Separator />
                <div className="space-y-2">
                    <h3 className="font-semibold font-headline">AI Reasoning Log</h3>
                    <ScrollArea className="h-24 w-full rounded-md border p-2">
                        <div className="space-y-2">
                            {commandLogs.map((log, index) => (
                                <Alert key={index}>
                                    <Bot className="h-4 w-4" />
                                    <AlertDescription>
                                        {log}
                                    </AlertDescription>
                                </Alert>
                            ))}
                        </div>
                    </ScrollArea>
                </div>
            </>
        )}

        {workflowLog.length > 0 && (
            <>
                <Separator />
                <div className="space-y-2">
                    <div className="flex items-center justify-between">
                        <h3 className="font-semibold font-headline">🔧 Workflow Log</h3>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={onToggleWorkflowLog}
                        >
                            {showWorkflowLog ? 'Hide' : 'Show'} ({workflowLog.length})
                        </Button>
                    </div>

                    {showWorkflowLog && (
                        <ScrollArea className="h-48 w-full rounded-md border p-2">
                            <div className="space-y-2">
                                {workflowLog.map((step, index) => (
                                    <Alert key={index} className="text-xs">
                                        <Bot className="h-3 w-3" />
                                        <AlertTitle className="text-xs font-mono">
                                            🔧 {step.toolName}
                                        </AlertTitle>
                                        <AlertDescription className="text-xs space-y-1">
                                            <div><strong>Args:</strong> {JSON.stringify(step.args)}</div>
                                            <div><strong>Result:</strong> {JSON.stringify(step.result)}</div>
                                            <div><strong>Reasoning:</strong> {step.reasoning}</div>
                                            <div className="text-muted-foreground">
                                                {new Date(step.timestamp).toLocaleTimeString()}
                                            </div>
                                        </AlertDescription>
                                    </Alert>
                                ))}
                            </div>
                        </ScrollArea>
                    )}
                </div>
            </>
        )}

        <Separator />

        {/* Removed test screens section */}

        {lastCommandDebugInfo ? (
          <div className="flex w-full gap-2">
            <Button onClick={onReset} variant="outline" className="flex-1">
              <RefreshCw className="mr-2 h-4 w-4" />
              Reset Game
            </Button>
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="secondary" className="flex-1">
                  <Bot className="mr-2 h-4 w-4" />
                  View Log
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-[80vw] md:max-w-[60vw]">
                <DialogHeader>
                  <DialogTitle>AI Debug Log</DialogTitle>
                  <DialogDescription>
                    The raw input and output for the last AI command.
                  </DialogDescription>
                </DialogHeader>
                <ScrollArea className="h-[70vh] rounded-md border">
                  <pre className="p-4 text-xs">
                    {JSON.stringify(lastCommandDebugInfo, null, 2)}
                  </pre>
                </ScrollArea>
              </DialogContent>
            </Dialog>
          </div>
        ) : (
          <Button onClick={onReset} variant="outline" className="w-full">
            <RefreshCw className="mr-2 h-4 w-4" />
            Reset Game
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
