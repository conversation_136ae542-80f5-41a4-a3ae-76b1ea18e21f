// Test script for tool-based mission planning
// Run with: node test-tool-based-mission.js

require('dotenv').config();

// Mock the tool functions for testing
function calculateDistances(botRow, botCol, targets) {
  return targets.map(target => ({
    target,
    distance: Math.abs(target.row - botRow) + Math.abs(target.col - botCol)
  })).sort((a, b) => a.distance - b.distance);
}

function findBestTarget(botRow, botCol, targets, obstacles = []) {
  const distances = calculateDistances(botRow, botCol, targets);
  
  // Score targets based on multiple factors
  const scored = distances.map(item => {
    let score = 0;
    
    // Prefer closer targets
    score += (20 - item.distance);
    
    // Prefer same row or column (easier path)
    if (item.target.row === botRow) score += 10; // Same row
    if (item.target.col === botCol) score += 15; // Same column (even better)
    
    // Penalize targets near obstacles
    const nearObstacles = obstacles.filter(obs => 
      Math.abs(obs.row - item.target.row) <= 1 && 
      Math.abs(obs.col - item.target.col) <= 1
    ).length;
    score -= nearObstacles * 3;
    
    return { ...item, score };
  });
  
  scored.sort((a, b) => b.score - a.score);
  return scored[0];
}

function getNextMove(botRow, botCol, targetRow, targetCol) {
  if (targetRow > botRow) return 'down';
  if (targetRow < botRow) return 'up';
  if (targetCol > botCol) return 'right';
  if (targetCol < botCol) return 'left';
  return 'none'; // Already at target
}

function checkLineOfSight(botRow, botCol, targetRow, targetCol) {
  const sameRow = botRow === targetRow;
  const sameCol = botCol === targetCol;
  
  if (sameRow) {
    return {
      canFire: true,
      direction: targetCol > botCol ? 'right' : 'left'
    };
  }
  
  if (sameCol) {
    return {
      canFire: true,
      direction: targetRow > botRow ? 'down' : 'up'
    };
  }
  
  return { canFire: false, direction: null };
}

function testToolBasedLogic() {
  console.log('🔧 Testing Tool-Based Mission Logic\n');

  // Test case: The problematic battery collection scenario
  const gameState = {
    botRow: 5,
    botCol: 10,
    objective: "collect all batteries",
    batteryLocations: [
      { row: 1, col: 6 },   // Distance: 8
      { row: 13, col: 10 }  // Distance: 8, same column
    ],
    turtleLocations: [
      { row: 2, col: 4 },
      { row: 5, col: 13 },
      { row: 11, col: 9 }
    ]
  };

  console.log('🎮 Game State:');
  console.log(`Bot: (${gameState.botRow}, ${gameState.botCol})`);
  console.log('Batteries:', gameState.batteryLocations.map(b => `(${b.row}, ${b.col})`).join(', '));
  console.log('Turtles:', gameState.turtleLocations.map(t => `(${t.row}, ${t.col})`).join(', '));
  console.log();

  // Simulate tool calls
  console.log('🔧 Tool Call 1: calculate_distances');
  const distances = calculateDistances(gameState.botRow, gameState.botCol, gameState.batteryLocations);
  console.log('Result:', distances);
  console.log();

  console.log('🔧 Tool Call 2: find_best_target');
  const bestTarget = findBestTarget(gameState.botRow, gameState.botCol, gameState.batteryLocations, gameState.turtleLocations);
  console.log('Result:', bestTarget);
  console.log();

  console.log('🔧 Tool Call 3: get_next_move');
  const nextMove = getNextMove(gameState.botRow, gameState.botCol, bestTarget.target.row, bestTarget.target.col);
  console.log(`Result: ${nextMove}`);
  console.log();

  // Analysis
  console.log('📊 Analysis:');
  console.log(`✅ Tool 1 found ${distances.length} targets with distances:`, distances.map(d => d.distance));
  
  if (bestTarget.target.col === gameState.botCol) {
    console.log('✅ Tool 2 correctly chose target in same column for easier path');
  } else {
    console.log('❌ Tool 2 chose suboptimal target');
  }
  
  if (nextMove === 'down' && bestTarget.target.row > gameState.botRow) {
    console.log('✅ Tool 3 correctly suggested "down" direction');
  } else {
    console.log(`❌ Tool 3 suggested "${nextMove}" but should be "down"`);
  }

  console.log('\n🎯 Expected Behavior:');
  console.log('1. Calculate distances: Both batteries have distance 8');
  console.log('2. Find best target: Choose (13,10) because same column = higher score');
  console.log('3. Get next move: "down" because 13 > 5');
  console.log('4. This should prevent loops and choose optimal paths!');

  // Test turtle elimination
  console.log('\n🐢 Testing Turtle Elimination:');
  const turtleGameState = {
    botRow: 7,
    botCol: 7,
    turtleLocations: [
      { row: 7, col: 10 }, // Same row - can fire
      { row: 10, col: 7 }, // Same column - can fire
      { row: 5, col: 5 }   // Diagonal - need to move
    ]
  };

  console.log(`Bot at (${turtleGameState.botRow}, ${turtleGameState.botCol})`);
  
  turtleGameState.turtleLocations.forEach((turtle, i) => {
    console.log(`\nTurtle ${i+1} at (${turtle.row}, ${turtle.col}):`);
    const lineOfSight = checkLineOfSight(turtleGameState.botRow, turtleGameState.botCol, turtle.row, turtle.col);
    console.log('  Line of sight:', lineOfSight);
    
    if (lineOfSight.canFire) {
      console.log(`  ✅ Can fire ${lineOfSight.direction} immediately`);
    } else {
      const move = getNextMove(turtleGameState.botRow, turtleGameState.botCol, turtle.row, turtle.col);
      console.log(`  📍 Need to move ${move} to get closer`);
    }
  });

  console.log('\n🚀 Tool-Based Advantages:');
  console.log('✅ Precise distance calculations');
  console.log('✅ Smart target scoring (same row/column preference)');
  console.log('✅ Clear line-of-sight detection');
  console.log('✅ Deterministic movement logic');
  console.log('✅ No ambiguity - tools provide exact answers');
}

testToolBasedLogic();
