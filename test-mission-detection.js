// Test script to debug mission detection
// Run with: node test-mission-detection.js

require('dotenv').config();

const { createStructuredCompletion } = require('./src/ai/openai-client.ts');
const { z } = require('zod');

const ProcessChatCommandOutputSchema = z.object({
  actions: z.array(z.object({
    action: z.enum(['move', 'fire', 'unknown']),
    direction: z.enum(['up', 'down', 'left', 'right', 'none']),
  })),
  response: z.string(),
  mission: z.enum(['collect_batteries', 'eliminate_turtles', 'none']).optional(),
  toolLog: z.string().nullable().optional(),
  reasoning: z.string().nullable().optional(),
});

async function testMissionDetection() {
  console.log('🔍 Testing Mission Detection Logic\n');

  const testCases = [
    "get all batteries",
    "collect all batteries", 
    "collect all the batteries",
    "get every battery",
    "kill all turtles",
    "eliminate all turtles",
    "destroy all turtles",
    "move up",
    "fire left"
  ];

  // Simple game state
  const gameState = {
    command: "", // Will be set for each test
    grid: Array(15).fill().map(() => Array(15).fill(' ')),
    botRow: 7,
    botCol: 7,
    turtleLocations: [{ row: 5, col: 5 }, { row: 10, col: 10 }],
    batteryLocations: [{ row: 3, col: 3 }, { row: 12, col: 12 }]
  };

  // Set bot position in grid
  gameState.grid[7][7] = '🤖';
  gameState.grid[5][5] = '🐢';
  gameState.grid[10][10] = '🐢';
  gameState.grid[3][3] = '🔋';
  gameState.grid[12][12] = '🔋';

  const gridDisplay = gameState.grid.map(row => row.join('')).join('\n');
  const batteryInfo = gameState.batteryLocations.map(loc => `  - (row: ${loc.row}, col: ${loc.col})`).join('\n');
  const turtleInfo = gameState.turtleLocations.map(loc => `  - (row: ${loc.row}, col: ${loc.col})`).join('\n');

  for (const command of testCases) {
    console.log(`Testing: "${command}"`);
    
    const prompt = `You are the AI controlling the bot in the Emoji Bot Arena game. Your task is to interpret a user's natural language command and translate it into a structured command for the game.

**Primary Task: Classify the Command**

You must first determine if the command is a **Simple Action** or a **High-Level Mission**.

1.  **Simple Actions**:
    *   **Description**: These are direct, explicit instructions that can be translated into one or more basic game actions immediately.
    *   **Examples**: "go left", "shoot up", "move left 3 units", "go up and then shoot right", "go to row 10, column 5".
    *   **Your Output**:
        *   Generate a complete sequence of one or more action objects in the 'actions' array.
        *   Set the \`mission\` field to "none" or omit it entirely.
        *   Provide a suitable \`response\`, e.g., "Okay, moving left three times."

2.  **High-Level Missions**:
    *   **Description**: These are broad, objective-based commands that require multiple steps to complete, like collecting all items of a certain type.
    *   **Examples**: "collect all the batteries", "get every battery", "eliminate all turtles", "clear the board of turtles".
    *   **Your Output**:
        *   You MUST NOT generate the step-by-step actions yourself.
        *   The 'actions' array MUST be empty.
        *   Identify the mission and set the \`mission\` field to either \`"collect_batteries"\` or \`"eliminate_turtles"\`.
        *   Provide a confirmation message in the \`response\` field, e.g., "Roger that. Starting mission: collect all batteries."

3.  **Unclear Commands**:
    *   If you cannot understand the command, set \`actions\` to \`[{ action: 'unknown', direction: 'none' }]\` and provide a clarifying \`response\`.

**Game Context:**
- User command: "${command}"
- Bot: (row: ${gameState.botRow}, col: ${gameState.botCol})
- Turtles:
${turtleInfo}
- Batteries:
${batteryInfo}
- Grid (for visual context):
${gridDisplay}

**VERY IMPORTANT JSON FORMATTING RULES:**
Your entire response MUST be a single, valid JSON object. Do not include any other text, explanations, or markdown formatting.

Respond with a JSON object in this exact format:
{
  "actions": [{"action": "move", "direction": "up"}],
  "response": "Moving up",
  "mission": "none",
  "toolLog": null,
  "reasoning": null
}`;

    try {
      const result = await createStructuredCompletion(prompt, ProcessChatCommandOutputSchema);
      
      console.log('  Result:');
      console.log('    Mission:', result.mission || 'none');
      console.log('    Response:', result.response);
      console.log('    Actions:', result.actions.length > 0 ? result.actions.map(a => `${a.action} ${a.direction}`).join(', ') : 'none');
      
      // Check if mission detection worked correctly
      if (command.toLowerCase().includes('all batteries') || command.toLowerCase().includes('every battery')) {
        if (result.mission === 'collect_batteries') {
          console.log('    ✅ Correctly detected battery collection mission');
        } else {
          console.log('    ❌ Failed to detect battery collection mission');
        }
      } else if (command.toLowerCase().includes('all turtles')) {
        if (result.mission === 'eliminate_turtles') {
          console.log('    ✅ Correctly detected turtle elimination mission');
        } else {
          console.log('    ❌ Failed to detect turtle elimination mission');
        }
      } else {
        if (result.mission === 'none' || !result.mission) {
          console.log('    ✅ Correctly identified as simple action');
        } else {
          console.log('    ❌ Incorrectly identified as mission');
        }
      }
      
    } catch (error) {
      console.log('    ❌ Error:', error.message);
    }
    
    console.log();
  }
}

if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_BASE) {
  testMissionDetection();
} else {
  console.log('⚠️  Please set OPENAI_API_KEY and OPENAI_API_BASE in .env file to run this test');
  console.log('Current values:');
  console.log('  OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? 'Set' : 'Not set');
  console.log('  OPENAI_API_BASE:', process.env.OPENAI_API_BASE || 'Not set');
}
