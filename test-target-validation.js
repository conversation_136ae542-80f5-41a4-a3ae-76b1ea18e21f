// Test script to verify target validation logic
// Run with: node test-target-validation.js

require('dotenv').config();

// Mock the process-chat-command logic for testing
function validateTarget(command, botRow, botCol, turtleLocations) {
  // Check if specific coordinates are mentioned
  const rowMatch = command.match(/row\s+(\d+)/i);
  const colMatch = command.match(/col(?:umn)?\s+(\d+)/i);
  
  if (rowMatch && colMatch) {
    const targetRow = parseInt(rowMatch[1]);
    const targetCol = parseInt(colMatch[1]);
    
    console.log(`Target specified: (${targetRow}, ${targetCol})`);
    console.log(`Bot position: (${botRow}, ${botCol})`);
    
    // Check if target is actually a turtle
    const targetIsTurtle = turtleLocations.some(turtle => 
      turtle.row === targetRow && turtle.col === targetCol
    );
    
    if (!targetIsTurtle) {
      return {
        valid: false,
        reason: `There is no turtle at row ${targetRow}, column ${targetCol}.`
      };
    }
    
    // Check if target is reachable (same row or column)
    const sameRow = targetRow === botRow;
    const sameCol = targetCol === botCol;
    
    console.log(`Same row: ${sameRow}, Same column: ${sameCol}`);
    
    if (!sameRow && !sameCol) {
      return {
        valid: false,
        reason: `Cannot fire at turtle at (${targetRow}, ${targetCol}). Bot at (${botRow}, ${botCol}) can only fire in straight lines. The turtle is not on the same row or column.`
      };
    }
    
    // Determine direction
    let direction;
    if (sameRow) {
      direction = targetCol < botCol ? 'left' : 'right';
    } else {
      direction = targetRow < botRow ? 'up' : 'down';
    }
    
    return {
      valid: true,
      direction,
      reason: `Can fire ${direction} at turtle at (${targetRow}, ${targetCol})`
    };
  }
  
  return { valid: false, reason: 'No specific target coordinates found' };
}

function runTests() {
  console.log('🧪 Testing Target Validation Logic\n');
  
  // Test case from the user's issue
  const testCase1 = {
    command: "kill turtle on row 5 and col 3",
    botRow: 7,
    botCol: 7,
    turtleLocations: [
      { row: 5, col: 3 },
      { row: 6, col: 12 },
      { row: 7, col: 9 },
      { row: 8, col: 8 },
      { row: 13, col: 0 }
    ]
  };
  
  console.log('Test 1: User\'s problematic case');
  console.log('Command:', testCase1.command);
  const result1 = validateTarget(testCase1.command, testCase1.botRow, testCase1.botCol, testCase1.turtleLocations);
  console.log('Result:', result1);
  console.log('Expected: Should be invalid (not same row/column)\n');
  
  // Test case where target is reachable
  const testCase2 = {
    command: "kill turtle on row 7 and col 9",
    botRow: 7,
    botCol: 7,
    turtleLocations: [
      { row: 5, col: 3 },
      { row: 6, col: 12 },
      { row: 7, col: 9 },
      { row: 8, col: 8 },
      { row: 13, col: 0 }
    ]
  };
  
  console.log('Test 2: Valid target (same row)');
  console.log('Command:', testCase2.command);
  const result2 = validateTarget(testCase2.command, testCase2.botRow, testCase2.botCol, testCase2.turtleLocations);
  console.log('Result:', result2);
  console.log('Expected: Should be valid, direction "right"\n');
  
  // Test case where target doesn't exist
  const testCase3 = {
    command: "kill turtle on row 10 and col 10",
    botRow: 7,
    botCol: 7,
    turtleLocations: [
      { row: 5, col: 3 },
      { row: 6, col: 12 },
      { row: 7, col: 9 },
      { row: 8, col: 8 },
      { row: 13, col: 0 }
    ]
  };
  
  console.log('Test 3: Non-existent target');
  console.log('Command:', testCase3.command);
  const result3 = validateTarget(testCase3.command, testCase3.botRow, testCase3.botCol, testCase3.turtleLocations);
  console.log('Result:', result3);
  console.log('Expected: Should be invalid (no turtle at location)\n');
  
  // Summary
  console.log('📋 Summary:');
  console.log('✅ Test 1:', result1.valid ? '❌ FAIL' : '✅ PASS', '- Correctly identified unreachable target');
  console.log('✅ Test 2:', result2.valid && result2.direction === 'right' ? '✅ PASS' : '❌ FAIL', '- Correctly identified reachable target');
  console.log('✅ Test 3:', !result3.valid ? '✅ PASS' : '❌ FAIL', '- Correctly identified non-existent target');
}

runTests();
