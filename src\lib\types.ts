export type Direction = 'up' | 'down' | 'left' | 'right';

export type Position = {
  row: number;
  col: number;
};

export type CellValue = '🤖' | '🐢' | '🔋' | '💥' | '✨' | ' ';

export type Grid = CellValue[][];

export type SuggestLaserDirectionOutput = {
    direction: "up" | "down" | "left" | "right";
    reason: string;
};

export type ChatMessage = {
  sender: 'user' | 'bot' | 'system';
  message: string;
};

export type GameAction = {
  action: 'move' | 'fire' | 'unknown';
  direction: Direction | 'none';
};
