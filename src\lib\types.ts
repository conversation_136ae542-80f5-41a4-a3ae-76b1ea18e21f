export type Direction = 'up' | 'down' | 'left' | 'right';

export type Position = {
  row: number;
  col: number;
};

export type CellValue = '🤖' | '🐢' | '🔋' | '💥' | '✨' | ' ';

export type Grid = CellValue[][];

// Removed SuggestLaserDirectionOutput - now using tool-based mission system

export type ChatMessage = {
  sender: 'user' | 'bot' | 'system';
  message: string;
};

export type GameAction = {
  action: 'move' | 'fire' | 'unknown';
  direction: Direction | 'none';
};
