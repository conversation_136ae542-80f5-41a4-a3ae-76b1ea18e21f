// Test script to verify mission planning logic
// Run with: node test-mission-planning.js

require('dotenv').config();

const { createStructuredCompletion } = require('./src/ai/openai-client.ts');
const { z } = require('zod');

const ActionSchema = z.object({
  action: z.enum(['move', 'fire']),
  direction: z.enum(['up', 'down', 'left', 'right']),
  reason: z.string(),
});

const PlanMissionOutputSchema = z.object({
  actions: z.array(ActionSchema),
  strategy: z.string(),
  estimatedSteps: z.number(),
});

async function testMissionPlanning() {
  console.log('🎯 Testing Mission Planning Logic\n');

  // Test case: Simple battery collection
  const testCase = {
    botRow: 5,
    botCol: 10,
    objective: "collect all batteries",
    batteryLocations: [
      { row: 1, col: 6 },
      { row: 13, col: 10 }
    ],
    turtleLocations: [
      { row: 2, col: 4 },
      { row: 5, col: 13 },
      { row: 11, col: 9 }
    ]
  };

  // Create a simple grid representation
  const grid = Array(15).fill().map((_, r) => 
    Array(15).fill().map((_, c) => {
      if (r === testCase.botRow && c === testCase.botCol) return '🤖';
      if (testCase.batteryLocations.some(b => b.row === r && b.col === c)) return '🔋';
      if (testCase.turtleLocations.some(t => t.row === r && t.col === c)) return '🐢';
      return ' ';
    })
  );

  const gridDisplay = grid.map(row => row.join('')).join('\n');
  const batteryInfo = testCase.batteryLocations.map(loc => `  - (${loc.row}, ${loc.col})`).join('\n');
  const turtleInfo = testCase.turtleLocations.map(loc => `  - (${loc.row}, ${loc.col})`).join('\n');

  const prompt = `You are an expert AI strategist for the Emoji Bot Arena game. Your task is to create a complete action sequence to accomplish the given mission efficiently.

**COORDINATE SYSTEM:**
- Row 0 is at the TOP, row numbers INCREASE going DOWN
- Column 0 is at the LEFT, column numbers INCREASE going RIGHT
- Moving 'up' DECREASES row number, 'down' INCREASES row number
- Moving 'left' DECREASES column, 'right' INCREASES column

**CURRENT GAME STATE:**
- Bot position: (${testCase.botRow}, ${testCase.botCol})
- Mission: "${testCase.objective}"
- Grid:
${gridDisplay}

- Batteries 🔋:
${batteryInfo}

- Turtles 🐢:
${turtleInfo}

**MISSION PLANNING RULES:**

**For "collect all batteries" missions:**
1. Calculate optimal visiting order using shortest path principles
2. Avoid turtles - plan safe routes around them
3. Prioritize batteries that are:
   - Closest by Manhattan distance
   - On same row/column (easier paths)
   - Away from turtle clusters

**PATHFINDING STRATEGY:**
1. Use Manhattan distance for initial estimates
2. Account for obstacles (turtles for battery missions)
3. Plan efficient routes that minimize backtracking
4. Consider safety - avoid turtle positions

**ACTION SEQUENCE REQUIREMENTS:**
- Generate COMPLETE sequence from start to finish
- Each action must be valid (no out-of-bounds moves)
- Include brief reason for each step
- Optimize for minimum total steps
- Ensure mission completion

Create a complete action plan that efficiently accomplishes the mission.

Respond with a JSON object in this exact format:
{
  "actions": [
    {"action": "move", "direction": "down", "reason": "Moving toward target"},
    {"action": "fire", "direction": "right", "reason": "Shooting turtle"}
  ],
  "strategy": "Overall strategy description",
  "estimatedSteps": 15
}`;

  try {
    console.log('🤖 Bot position:', `(${testCase.botRow}, ${testCase.botCol})`);
    console.log('🔋 Batteries:', testCase.batteryLocations.map(b => `(${b.row}, ${b.col})`).join(', '));
    console.log('🐢 Turtles:', testCase.turtleLocations.map(t => `(${t.row}, ${t.col})`).join(', '));
    console.log('\n📋 Requesting mission plan...\n');

    const result = await createStructuredCompletion(prompt, PlanMissionOutputSchema);
    
    console.log('✅ Mission Plan Generated:');
    console.log('Strategy:', result.strategy);
    console.log('Estimated Steps:', result.estimatedSteps);
    console.log('\n📝 Action Sequence:');
    
    result.actions.forEach((action, i) => {
      console.log(`${i + 1}. ${action.action.toUpperCase()} ${action.direction} - ${action.reason}`);
    });

    // Validate the plan
    console.log('\n🔍 Plan Analysis:');
    
    let currentRow = testCase.botRow;
    let currentCol = testCase.botCol;
    let batteriesCollected = 0;
    let validPlan = true;
    
    for (let i = 0; i < result.actions.length; i++) {
      const action = result.actions[i];
      
      if (action.action === 'move') {
        // Simulate movement
        if (action.direction === 'up') currentRow--;
        else if (action.direction === 'down') currentRow++;
        else if (action.direction === 'left') currentCol--;
        else if (action.direction === 'right') currentCol++;
        
        // Check bounds
        if (currentRow < 0 || currentRow >= 15 || currentCol < 0 || currentCol >= 15) {
          console.log(`❌ Step ${i + 1}: Out of bounds move to (${currentRow}, ${currentCol})`);
          validPlan = false;
          break;
        }
        
        // Check if reached a battery
        const batteryHere = testCase.batteryLocations.find(b => b.row === currentRow && b.col === currentCol);
        if (batteryHere) {
          batteriesCollected++;
          console.log(`✅ Step ${i + 1}: Collected battery at (${currentRow}, ${currentCol})`);
        }
        
        // Check if hit a turtle
        const turtleHere = testCase.turtleLocations.find(t => t.row === currentRow && t.col === currentCol);
        if (turtleHere) {
          console.log(`❌ Step ${i + 1}: Hit turtle at (${currentRow}, ${currentCol}) - GAME OVER`);
          validPlan = false;
          break;
        }
      }
    }
    
    console.log(`\n📊 Results:`);
    console.log(`Batteries collected: ${batteriesCollected}/${testCase.batteryLocations.length}`);
    console.log(`Plan validity: ${validPlan ? '✅ Valid' : '❌ Invalid'}`);
    console.log(`Mission success: ${batteriesCollected === testCase.batteryLocations.length && validPlan ? '✅ Success' : '❌ Failed'}`);
    
  } catch (error) {
    console.error('❌ Mission planning failed:', error.message);
    console.log('\n💡 This might indicate:');
    console.log('- API configuration issues');
    console.log('- Model limitations with complex planning');
    console.log('- Need for simpler planning approach');
  }
}

if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_BASE) {
  testMissionPlanning();
} else {
  console.log('⚠️  Please set OPENAI_API_KEY and OPENAI_API_BASE in .env file to run this test');
}
