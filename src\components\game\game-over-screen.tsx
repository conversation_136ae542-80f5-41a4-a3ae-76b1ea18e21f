'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Volume2, VolumeX, RotateCcw, Trophy, Skull } from 'lucide-react';

interface GameOverScreenProps {
  isWin: boolean;
  score: number;
  onRestart: () => void;
  onClose: () => void;
}

export function GameOverScreen({ isWin, score, onRestart, onClose }: GameOverScreenProps) {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isMuted, setIsMuted] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    // Play appropriate music when component mounts
    if (audioRef.current) {
      audioRef.current.volume = 0.5; // Set volume to 50%
      audioRef.current.play().then(() => {
        setIsPlaying(true);
      }).catch((error) => {
        console.log('Audio autoplay prevented:', error);
      });
    }

    // Cleanup function to stop audio when component unmounts
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
    };
  }, []);

  const toggleMute = () => {
    if (audioRef.current) {
      audioRef.current.muted = !audioRef.current.muted;
      setIsMuted(!isMuted);
    }
  };

  const handleRestart = () => {
    // Stop music before restarting
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
    onRestart();
  };

  const handleClose = useCallback(() => {
    console.log('🔴 Close button clicked - stopping audio and closing');
    // Stop music before closing
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
    }
    // Call the parent's onClose function
    onClose();
  }, [onClose]);

  const audioSrc = isWin ? '/youwin.mp3' : '/gameover.mp3';
  const imageSrc = isWin ? '/youlose.png' : '/youwin.png'; // Swapped because images are inverted
  const title = isWin ? 'Victory!' : 'Game Over';
  const message = isWin 
    ? 'Congratulations! You completed the mission!' 
    : 'Better luck next time!';

  return (
    <div
      className="fixed inset-0 bg-gradient-to-br from-slate-900/95 via-purple-900/95 to-slate-900/95 backdrop-blur-md flex items-center justify-center z-50 p-4 animate-in fade-in duration-500"
      onClick={(e) => {
        // Prevent backdrop clicks from closing the modal
        e.stopPropagation();
      }}
    >
      {/* Cyberpunk background effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-cyan-400/60 to-transparent animate-pulse"></div>
        <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-purple-400/60 to-transparent animate-pulse"></div>
        <div className="absolute top-0 left-0 h-full w-0.5 bg-gradient-to-b from-transparent via-pink-400/60 to-transparent animate-pulse"></div>
        <div className="absolute top-0 right-0 h-full w-0.5 bg-gradient-to-b from-transparent via-cyan-400/60 to-transparent animate-pulse"></div>
      </div>
      {/* Background music */}
      <audio
        ref={audioRef}
        src={audioSrc}
        loop
        onEnded={() => setIsPlaying(false)}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
      />

      <Card
        className="w-full max-w-md mx-auto animate-in slide-in-from-bottom-4 duration-700 shadow-[0_0_50px_rgba(34,211,238,0.3)] bg-slate-900/90 border-cyan-500/50 backdrop-blur-sm"
        onClick={(e) => e.stopPropagation()}
      >
        <CardContent className="p-8 text-center space-y-6 text-cyan-100">
          {/* Cyberpunk header decoration */}
          <div className="absolute top-2 left-2 right-2 h-0.5 bg-gradient-to-r from-cyan-500/50 via-purple-500/50 to-pink-500/50"></div>
          <div className="absolute bottom-2 left-2 right-2 h-0.5 bg-gradient-to-r from-pink-500/50 via-purple-500/50 to-cyan-500/50"></div>
          {/* Game Over Image */}
          <div className="relative">
            <img 
              src={imageSrc} 
              alt={title}
              className="w-full h-48 object-contain mx-auto"
              onError={(e) => {
                // Fallback if image fails to load
                e.currentTarget.style.display = 'none';
              }}
            />
          </div>

          {/* Title with Icon */}
          <div className="space-y-4">
            <div className="flex items-center justify-center gap-3">
              {isWin ? (
                <Trophy className="h-10 w-10 text-yellow-400 animate-pulse" />
              ) : (
                <Skull className="h-10 w-10 text-red-400 animate-pulse" />
              )}
              <h1 className={`text-4xl font-bold font-mono ${
                isWin
                  ? 'text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 via-green-400 to-cyan-400'
                  : 'text-transparent bg-clip-text bg-gradient-to-r from-red-400 via-orange-400 to-pink-400'
              }`}>
                {isWin ? 'MISSION_SUCCESS' : 'SYSTEM_FAILURE'}
              </h1>
            </div>
            <p className="text-lg text-cyan-300/80 font-mono">{message}</p>
          </div>

          {/* Score Display */}
          <div className="bg-gradient-to-br from-slate-800/50 to-slate-700/50 border border-cyan-500/30 rounded-lg p-4 shadow-[0_0_15px_rgba(34,211,238,0.2)]">
            <p className="text-sm text-cyan-400/70 font-mono">FINAL_SCORE</p>
            <p className="text-3xl font-bold font-mono text-cyan-300">{score}</p>
            <div className="mt-2 h-1 bg-slate-700 rounded-full overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-cyan-500 to-purple-500 rounded-full animate-pulse"
                style={{ width: `${Math.min(100, (score / 100) * 100)}%` }}
              ></div>
            </div>
          </div>

          {/* Audio Controls */}
          <div className="flex items-center justify-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={toggleMute}
              className="flex items-center gap-2"
            >
              {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
              {isMuted ? 'Unmute' : 'Mute'}
            </Button>
            {isPlaying && !isMuted && (
              <span className="text-xs text-muted-foreground">♪ Playing</span>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              onClick={handleRestart}
              className={`flex-1 font-mono ${
                isWin
                  ? 'bg-gradient-to-br from-green-600/80 to-cyan-600/80 hover:from-green-500 hover:to-cyan-500 border border-green-400/50 shadow-[0_0_15px_rgba(34,197,94,0.4)]'
                  : 'bg-gradient-to-br from-purple-600/80 to-pink-600/80 hover:from-purple-500 hover:to-pink-500 border border-purple-400/50 shadow-[0_0_15px_rgba(168,85,247,0.4)]'
              }`}
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              RESTART_SYS
            </Button>
            <Button
              onClick={() => {
                console.log('🔴 Direct close button click');
                // Stop audio immediately
                if (audioRef.current) {
                  audioRef.current.pause();
                  audioRef.current.currentTime = 0;
                }
                // Force close
                onClose();
              }}
              className="flex-1 bg-gradient-to-br from-slate-600/80 to-slate-700/80 hover:from-slate-500 hover:to-slate-600 border border-slate-400/50 shadow-[0_0_10px_rgba(100,116,139,0.4)] font-mono text-cyan-100"
              type="button"
            >
              EXIT_PROG
            </Button>
          </div>

          {/* Fun Stats or Tips */}
          <div className="text-xs text-muted-foreground space-y-1">
            {isWin ? (
              <>
                <p>🎉 Mission accomplished!</p>
                <p>🤖 Your bot performed excellently!</p>
              </>
            ) : (
              <>
                <p>💡 Tip: Try using missions like "collect all batteries"</p>
                <p>🎯 Or "kill all turtles" for strategic gameplay</p>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
