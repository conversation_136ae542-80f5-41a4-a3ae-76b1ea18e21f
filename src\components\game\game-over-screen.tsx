'use client';

import { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Volume2, VolumeX, RotateCcw, Trophy, Skull } from 'lucide-react';

interface GameOverScreenProps {
  isWin: boolean;
  score: number;
  onRestart: () => void;
  onClose: () => void;
}

export function GameOverScreen({ isWin, score, onRestart, onClose }: GameOverScreenProps) {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isMuted, setIsMuted] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    // Play appropriate music when component mounts
    if (audioRef.current) {
      audioRef.current.volume = 0.5; // Set volume to 50%
      audioRef.current.play().then(() => {
        setIsPlaying(true);
      }).catch((error) => {
        console.log('Audio autoplay prevented:', error);
      });
    }

    // Cleanup function to stop audio when component unmounts
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
    };
  }, []);

  const toggleMute = () => {
    if (audioRef.current) {
      audioRef.current.muted = !audioRef.current.muted;
      setIsMuted(!isMuted);
    }
  };

  const handleRestart = () => {
    // Stop music before restarting
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
    onRestart();
  };

  const handleClose = () => {
    console.log('🔴 Close button clicked');
    // Stop music before closing
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
    console.log('🔴 Calling onClose function');
    onClose();
  };

  const audioSrc = isWin ? '/youwin.mp3' : '/gameover.mp3';
  const imageSrc = isWin ? '/youlose.png' : '/youwin.png'; // Swapped because images are inverted
  const title = isWin ? 'Victory!' : 'Game Over';
  const message = isWin 
    ? 'Congratulations! You completed the mission!' 
    : 'Better luck next time!';

  return (
    <div
      className="fixed inset-0 bg-black/90 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-in fade-in duration-500"
      onClick={(e) => {
        // Prevent backdrop clicks from closing the modal
        e.stopPropagation();
      }}
    >
      {/* Background music */}
      <audio
        ref={audioRef}
        src={audioSrc}
        loop
        onEnded={() => setIsPlaying(false)}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
      />

      <Card
        className="w-full max-w-md mx-auto animate-in slide-in-from-bottom-4 duration-700 shadow-2xl"
        onClick={(e) => e.stopPropagation()}
      >
        <CardContent className="p-8 text-center space-y-6">
          {/* Game Over Image */}
          <div className="relative">
            <img 
              src={imageSrc} 
              alt={title}
              className="w-full h-48 object-contain mx-auto"
              onError={(e) => {
                // Fallback if image fails to load
                e.currentTarget.style.display = 'none';
              }}
            />
          </div>

          {/* Title with Icon */}
          <div className="space-y-2">
            <div className="flex items-center justify-center gap-2">
              {isWin ? (
                <Trophy className="h-8 w-8 text-yellow-500" />
              ) : (
                <Skull className="h-8 w-8 text-red-500" />
              )}
              <h1 className={`text-3xl font-bold ${
                isWin ? 'text-green-600' : 'text-red-600'
              }`}>
                {title}
              </h1>
            </div>
            <p className="text-lg text-muted-foreground">{message}</p>
          </div>

          {/* Score Display */}
          <div className="bg-muted rounded-lg p-4">
            <p className="text-sm text-muted-foreground">Final Score</p>
            <p className="text-2xl font-bold">{score}</p>
          </div>

          {/* Audio Controls */}
          <div className="flex items-center justify-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={toggleMute}
              className="flex items-center gap-2"
            >
              {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
              {isMuted ? 'Unmute' : 'Mute'}
            </Button>
            {isPlaying && !isMuted && (
              <span className="text-xs text-muted-foreground">♪ Playing</span>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button 
              onClick={handleRestart} 
              className="flex-1"
              variant={isWin ? "default" : "secondary"}
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              Play Again
            </Button>
            <Button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleClose();
              }}
              variant="outline"
              className="flex-1"
            >
              Close
            </Button>
          </div>

          {/* Fun Stats or Tips */}
          <div className="text-xs text-muted-foreground space-y-1">
            {isWin ? (
              <>
                <p>🎉 Mission accomplished!</p>
                <p>🤖 Your bot performed excellently!</p>
              </>
            ) : (
              <>
                <p>💡 Tip: Try using missions like "collect all batteries"</p>
                <p>🎯 Or "kill all turtles" for strategic gameplay</p>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
