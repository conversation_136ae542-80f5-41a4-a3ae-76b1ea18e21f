'use server';

/**
 * @fileOverview A function that suggests the optimal direction to fire the laser in the Emoji Bot Arena game.
 *
 * - suggestLaserDirection - A function that suggests the best direction to fire the laser.
 * - SuggestLaserDirectionInput - The input type for the suggestLaserDirection function.
 * - SuggestLaserDirectionOutput - The return type for the suggestLaserDirection function.
 */

import { createStructuredCompletion } from '@/ai/openai-client';
import { z } from 'zod';
import { SuggestLaserDirectionInputSchema } from '@/ai/schemas';

export type SuggestLaserDirectionInput = z.infer<
  typeof SuggestLaserDirectionInputSchema
>;

const SuggestLaserDirectionOutputSchema = z.object({
  direction: z
    .enum(['up', 'down', 'left', 'right'])
    .describe('The suggested direction to fire the laser.'),
  reason: z.string().describe('The reasoning behind the suggested direction.'),
});
export type SuggestLaserDirectionOutput = z.infer<
  typeof SuggestLaserDirectionOutputSchema
>;

export async function suggestLaserDirection(
  input: SuggestLaserDirectionInput
): Promise<SuggestLaserDirectionOutput> {
  const gridDisplay = input.grid.map(row => row.join('')).join('\n');

  const prompt = `You are an AI assistant playing the Emoji Bot Arena game. The game is played on a grid of emoji.
Your task is to suggest the optimal direction for the bot to fire its laser to eliminate turtles and improve its score.

**CRITICAL FIRING RULES:**
- Lasers travel in straight lines (horizontal or vertical only)
- Bot at (${input.botRow}, ${input.botCol}) can only hit targets that are:
  * On the SAME ROW (row ${input.botRow}) when firing left/right
  * On the SAME COLUMN (column ${input.botCol}) when firing up/down
- Firing directions:
  * UP: hits targets at (0 to ${input.botRow-1}, ${input.botCol})
  * DOWN: hits targets at (${input.botRow+1} to 14, ${input.botCol})
  * LEFT: hits targets at (${input.botRow}, 0 to ${input.botCol-1})
  * RIGHT: hits targets at (${input.botRow}, ${input.botCol+1} to 14)

Here is the current state of the grid:

${gridDisplay}

The bot is located at row ${input.botRow}, column ${input.botCol}.

**ANALYSIS REQUIRED:**
1. Check each direction for turtles that can actually be hit
2. Verify no batteries block the path to turtles
3. If no turtles are hittable, choose direction that clears path to batteries or has strategic value

**IMPORTANT:** Only suggest directions where turtles are actually reachable in a straight line from the bot's position.

Respond with a JSON object in this exact format:
{
  "direction": "<direction>",
  "reason": "<reason>"
}

Make sure the direction is one of: up, down, left, or right.`;

  return await createStructuredCompletion<SuggestLaserDirectionOutput>(
    prompt,
    SuggestLaserDirectionOutputSchema
  );
}
