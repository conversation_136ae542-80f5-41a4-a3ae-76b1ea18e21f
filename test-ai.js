// Simple test script to verify OpenAI integration
// Run with: node test-ai.js

require('dotenv').config();

const OpenAI = require('openai');

async function testOpenAIConnection() {
  console.log('Testing OpenAI connection...');
  console.log('API Key:', process.env.OPENAI_API_KEY ? 'Set' : 'Not set');
  console.log('API Base:', process.env.OPENAI_API_BASE || 'Not set');

  if (!process.env.OPENAI_API_KEY) {
    console.error('❌ OPENAI_API_KEY is not set in .env file');
    return;
  }

  if (!process.env.OPENAI_API_BASE) {
    console.error('❌ OPENAI_API_BASE is not set in .env file');
    return;
  }

  try {
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
      baseURL: process.env.OPENAI_API_BASE,
    });

    console.log('Making test request...');
    
    const completion = await openai.chat.completions.create({
      model: 'codestral-latest', // Change this to match your model name
      messages: [
        {
          role: 'user',
          content: 'Respond with a JSON object containing a "status" field set to "success" and a "message" field with a brief greeting.',
        },
      ],
      temperature: 0.1,
      response_format: { type: 'json_object' },
    });

    const content = completion.choices[0]?.message?.content;
    console.log('✅ Response received:', content);
    
    try {
      const parsed = JSON.parse(content);
      console.log('✅ JSON parsing successful:', parsed);
    } catch (parseError) {
      console.error('❌ Failed to parse JSON response:', parseError);
    }

  } catch (error) {
    console.error('❌ OpenAI API Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testOpenAIConnection();
