// Test script to compare strategic vs single-step planning
// Run with: node test-strategic-vs-single-step.js

require('dotenv').config();

const { createStructuredCompletion } = require('./src/ai/openai-client.ts');
const { z } = require('zod');

// Schemas for both approaches
const SingleStepOutputSchema = z.object({
  direction: z.enum(['up', 'down', 'left', 'right', 'none']),
  reason: z.string(),
});

const StrategicOutputSchema = z.object({
  primaryTarget: z.object({ row: z.number(), col: z.number() }),
  strategy: z.string(),
  nextAction: z.enum(['move', 'fire']),
  direction: z.enum(['up', 'down', 'left', 'right']),
  reason: z.string(),
  alternativeTargets: z.array(z.object({ row: z.number(), col: z.number() })),
});

async function testSingleStepApproach(gameState) {
  const { botRow, botCol, objective, batteryLocations, turtleLocations } = gameState;
  
  const gridDisplay = Array(15).fill().map((_, r) => 
    Array(15).fill().map((_, c) => {
      if (r === botRow && c === botCol) return '🤖';
      if (batteryLocations.some(b => b.row === r && b.col === c)) return '🔋';
      if (turtleLocations.some(t => t.row === r && t.col === c)) return '🐢';
      return ' ';
    }).join('')
  ).join('\n');

  const batteryInfo = batteryLocations.map(loc => `  - row ${loc.row}, col ${loc.col}`).join('\n');
  const turtleInfo = turtleLocations.map(loc => `  - row ${loc.row}, col ${loc.col}`).join('\n');

  const prompt = `You are a meticulous and logical AI assistant playing the Emoji Bot Arena game. Your task is to suggest the single most optimal move for the bot to achieve a given objective.

**COORDINATE SYSTEM:**
- Row 0 is at the TOP, row numbers INCREASE going DOWN
- Moving 'up' DECREASES row number, 'down' INCREASES row number
- Moving 'left' DECREASES column, 'right' INCREASES column

**GAME STATE:**
- Bot 🤖 at row ${botRow}, column ${botCol}
- Objective: "${objective}"
- Grid:
${gridDisplay}
- Batteries 🔋:
${batteryInfo}
- Turtles 🐢:
${turtleInfo}

**For batteries:** Find closest battery, move toward it using Manhattan distance.
**Direction Logic:** If target row > bot row: move 'down', if target row < bot row: move 'up', etc.

Respond with JSON: {"direction": "down", "reason": "explanation"}`;

  return await createStructuredCompletion(prompt, SingleStepOutputSchema);
}

async function testStrategicApproach(gameState) {
  const { botRow, botCol, objective, batteryLocations, turtleLocations } = gameState;
  
  const gridDisplay = Array(15).fill().map((_, r) => 
    Array(15).fill().map((_, c) => {
      if (r === botRow && c === botCol) return '🤖';
      if (batteryLocations.some(b => b.row === r && b.col === c)) return '🔋';
      if (turtleLocations.some(t => t.row === r && t.col === c)) return '🐢';
      return ' ';
    }).join('')
  ).join('\n');

  const batteryInfo = batteryLocations.map((loc, i) => 
    `  ${i+1}. (${loc.row}, ${loc.col}) - distance: ${Math.abs(loc.row - botRow) + Math.abs(loc.col - botCol)}`
  ).join('\n');
  
  const turtleInfo = turtleLocations.map((loc, i) => 
    `  ${i+1}. (${loc.row}, ${loc.col}) - distance: ${Math.abs(loc.row - botRow) + Math.abs(loc.col - botCol)}`
  ).join('\n');

  const prompt = `You are a strategic AI advisor for the Emoji Bot Arena game. Provide high-level strategy and determine the best immediate action.

**COORDINATE SYSTEM:**
- Row 0 is at the TOP, row numbers INCREASE going DOWN
- Moving 'up' DECREASES row number, 'down' INCREASES row number

**GAME STATE:**
- Bot position: (${botRow}, ${botCol})
- Mission: "${objective}"
- Grid:
${gridDisplay}
- Batteries 🔋 (with distances):
${batteryInfo}
- Turtles 🐢 (with distances):
${turtleInfo}

**STRATEGIC ANALYSIS:**
1. Analyze all targets with pros/cons
2. Choose PRIMARY target (best overall choice)
3. Consider path efficiency, safety, and strategic positioning
4. Provide alternative targets as backup options

**For batteries:** Prioritize by distance, safety, and path efficiency (same row/column preferred).

Respond with JSON:
{
  "primaryTarget": {"row": 10, "col": 5},
  "strategy": "Strategic explanation",
  "nextAction": "move",
  "direction": "down",
  "reason": "Detailed reasoning",
  "alternativeTargets": [{"row": 3, "col": 8}]
}`;

  return await createStructuredCompletion(prompt, StrategicOutputSchema);
}

async function compareApproaches() {
  console.log('🆚 Comparing Strategic vs Single-Step Planning\n');

  // Test case: The problematic battery collection scenario
  const gameState = {
    botRow: 5,
    botCol: 10,
    objective: "collect all batteries",
    batteryLocations: [
      { row: 1, col: 6 },   // Distance: 8
      { row: 13, col: 10 }  // Distance: 8, same column
    ],
    turtleLocations: [
      { row: 2, col: 4 },
      { row: 5, col: 13 },
      { row: 11, col: 9 }
    ]
  };

  console.log('🎮 Game State:');
  console.log(`Bot: (${gameState.botRow}, ${gameState.botCol})`);
  console.log('Batteries:', gameState.batteryLocations.map(b => `(${b.row}, ${b.col})`).join(', '));
  console.log('Turtles:', gameState.turtleLocations.map(t => `(${t.row}, ${t.col})`).join(', '));
  console.log();

  try {
    // Test single-step approach
    console.log('🔄 Testing Single-Step Approach...');
    const singleStep = await testSingleStepApproach(gameState);
    console.log('Result:', singleStep);
    console.log();

    // Test strategic approach
    console.log('🧠 Testing Strategic Approach...');
    const strategic = await testStrategicApproach(gameState);
    console.log('Primary Target:', `(${strategic.primaryTarget.row}, ${strategic.primaryTarget.col})`);
    console.log('Strategy:', strategic.strategy);
    console.log('Next Action:', `${strategic.nextAction} ${strategic.direction}`);
    console.log('Reasoning:', strategic.reason);
    console.log('Alternatives:', strategic.alternativeTargets.map(t => `(${t.row}, ${t.col})`).join(', '));
    console.log();

    // Analysis
    console.log('📊 Analysis:');
    
    // Check if both chose the same direction
    if (singleStep.direction === strategic.direction) {
      console.log('✅ Both approaches agree on direction:', singleStep.direction);
    } else {
      console.log('❓ Approaches disagree:');
      console.log(`  Single-step: ${singleStep.direction}`);
      console.log(`  Strategic: ${strategic.direction}`);
    }

    // Evaluate target choice
    const battery1Distance = Math.abs(1 - gameState.botRow) + Math.abs(6 - gameState.botCol);
    const battery2Distance = Math.abs(13 - gameState.botRow) + Math.abs(10 - gameState.botCol);
    
    console.log('\n🎯 Target Analysis:');
    console.log(`Battery 1 (1,6): distance ${battery1Distance}`);
    console.log(`Battery 2 (13,10): distance ${battery2Distance} - same column!`);
    
    if (strategic.primaryTarget.row === 13 && strategic.primaryTarget.col === 10) {
      console.log('✅ Strategic approach chose the better target (same column)');
    } else if (strategic.primaryTarget.row === 1 && strategic.primaryTarget.col === 6) {
      console.log('⚠️ Strategic approach chose the harder target');
    }

    // Expected optimal choice
    console.log('\n💡 Optimal Strategy:');
    console.log('Should target battery at (13,10) because:');
    console.log('- Same distance as (1,6)');
    console.log('- Same column = straight path down');
    console.log('- No turtles blocking the path');
    console.log('- Direction should be "down"');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_BASE) {
  compareApproaches();
} else {
  console.log('⚠️  Please set OPENAI_API_KEY and OPENAI_API_BASE in .env file to run this test');
}
