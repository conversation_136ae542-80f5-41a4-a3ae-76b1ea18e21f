// Diagnostic script to check setup
// Run with: node diagnose-setup.js

require('dotenv').config();

console.log('🔍 Diagnosing Setup\n');

console.log('📁 Environment Variables:');
console.log('  OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? `Set (${process.env.OPENAI_API_KEY.substring(0, 10)}...)` : '❌ Not set');
console.log('  OPENAI_API_BASE:', process.env.OPENAI_API_BASE || '❌ Not set');
console.log();

console.log('📦 Dependencies:');
try {
  const openai = require('openai');
  console.log('  ✅ OpenAI package installed');
} catch (error) {
  console.log('  ❌ OpenAI package not found');
}

try {
  const zod = require('zod');
  console.log('  ✅ Zod package installed');
} catch (error) {
  console.log('  ❌ Zod package not found');
}

console.log();

console.log('🎮 Game Files:');
const fs = require('fs');
const path = require('path');

const filesToCheck = [
  'src/ai/openai-client.ts',
  'src/ai/flows/process-chat-command.ts',
  'src/ai/flows/suggest-move-direction.ts',
  'src/components/game/emoji-bot-arena.tsx'
];

filesToCheck.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file} - Missing!`);
  }
});

console.log();

console.log('🚀 Next Steps:');
if (!process.env.OPENAI_API_KEY || !process.env.OPENAI_API_BASE) {
  console.log('❌ Missing API configuration:');
  console.log('   1. Edit .env file');
  console.log('   2. Set OPENAI_API_KEY=your-codestral-api-key');
  console.log('   3. Set OPENAI_API_BASE=your-codestral-api-base-url');
  console.log('   4. Restart the development server');
} else {
  console.log('✅ API configuration looks good');
  console.log('✅ Mission detection fallback is now active');
  console.log('✅ Try "get all batteries" in the game - it should work now!');
}

console.log();
console.log('🔧 If still not working:');
console.log('   1. Check browser console for errors (F12)');
console.log('   2. Check terminal running "npm run dev" for errors');
console.log('   3. Try refreshing the page');
console.log('   4. The fallback should work even without API configuration');
