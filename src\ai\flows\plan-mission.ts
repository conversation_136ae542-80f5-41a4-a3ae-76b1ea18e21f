'use server';

/**
 * @fileOverview A function that generates complete action sequences for missions.
 *
 * - planMission - A function that creates a full plan to complete a mission
 * - PlanMissionInput - The input type for the planMission function
 * - PlanMissionOutput - The return type for the planMission function
 */

import { createStructuredCompletion } from '@/ai/openai-client';
import { z } from 'zod';

const LocationSchema = z.object({
  row: z.number(),
  col: z.number(),
});

const ActionSchema = z.object({
  action: z.enum(['move', 'fire']).describe("The game action to perform."),
  direction: z.enum(['up', 'down', 'left', 'right']).describe("The direction for the action."),
  reason: z.string().describe("Brief explanation for this step."),
});

const PlanMissionInputSchema = z.object({
  grid: z.array(z.array(z.string())).describe('A 2D array representing the game grid.'),
  botRow: z.number().describe('The row index of the bot on the grid.'),
  botCol: z.number().describe('The column index of the bot on the grid.'),
  objective: z.string().describe('The mission objective (e.g., "collect all batteries", "eliminate all turtles").'),
  turtleLocations: z.array(LocationSchema).describe('The locations of all turtles on the grid.'),
  batteryLocations: z.array(LocationSchema).describe('The locations of all batteries on the grid.'),
});

export type PlanMissionInput = z.infer<typeof PlanMissionInputSchema>;

const PlanMissionOutputSchema = z.object({
  actions: z.array(ActionSchema).describe("Complete sequence of actions to complete the mission."),
  strategy: z.string().describe('Overall strategy explanation.'),
  estimatedSteps: z.number().describe('Estimated number of steps to complete.'),
});

export type PlanMissionOutput = z.infer<typeof PlanMissionOutputSchema>;

export async function planMission(input: PlanMissionInput): Promise<PlanMissionOutput> {
  const gridDisplay = input.grid.map(row => row.join('')).join('\n');
  
  let batteryInfo = 'None';
  if (input.batteryLocations && input.batteryLocations.length > 0) {
    batteryInfo = input.batteryLocations.map(loc => `  - (${loc.row}, ${loc.col})`).join('\n');
  }
  
  let turtleInfo = 'None';
  if (input.turtleLocations && input.turtleLocations.length > 0) {
    turtleInfo = input.turtleLocations.map(loc => `  - (${loc.row}, ${loc.col})`).join('\n');
  }

  const prompt = `You are an expert AI strategist for the Emoji Bot Arena game. Your task is to create a complete action sequence to accomplish the given mission efficiently.

**COORDINATE SYSTEM:**
- Row 0 is at the TOP, row numbers INCREASE going DOWN
- Column 0 is at the LEFT, column numbers INCREASE going RIGHT
- Moving 'up' DECREASES row number, 'down' INCREASES row number
- Moving 'left' DECREASES column, 'right' INCREASES column

**CURRENT GAME STATE:**
- Bot position: (${input.botRow}, ${input.botCol})
- Mission: "${input.objective}"
- Grid:
${gridDisplay}

- Batteries 🔋:
${batteryInfo}

- Turtles 🐢:
${turtleInfo}

**MISSION PLANNING RULES:**

**For "collect all batteries" missions:**
1. Calculate optimal visiting order using shortest path principles
2. Avoid turtles - plan safe routes around them
3. Prioritize batteries that are:
   - Closest by Manhattan distance
   - On same row/column (easier paths)
   - Away from turtle clusters

**For "eliminate all turtles" missions:**
1. Plan positioning to align with turtles for clear shots
2. Consider firing through multiple turtles when possible
3. Avoid moving into turtle positions
4. Plan movement to get line-of-sight (same row or column)

**PATHFINDING STRATEGY:**
1. Use Manhattan distance for initial estimates
2. Account for obstacles (turtles for battery missions)
3. Plan efficient routes that minimize backtracking
4. Consider safety - avoid turtle positions

**ACTION SEQUENCE REQUIREMENTS:**
- Generate COMPLETE sequence from start to finish
- Each action must be valid (no out-of-bounds moves)
- Include brief reason for each step
- Optimize for minimum total steps
- Ensure mission completion

**EXAMPLE ACTION FORMAT:**
{
  "action": "move",
  "direction": "down",
  "reason": "Moving toward battery at (10,5)"
}

Create a complete action plan that efficiently accomplishes the mission.

Respond with a JSON object in this exact format:
{
  "actions": [
    {"action": "move", "direction": "down", "reason": "Moving toward target"},
    {"action": "fire", "direction": "right", "reason": "Shooting turtle"}
  ],
  "strategy": "Overall strategy description",
  "estimatedSteps": 15
}`;

  return await createStructuredCompletion<PlanMissionOutput>(
    prompt,
    PlanMissionOutputSchema
  );
}
