// Test script to verify battery collection logic
// Run with: node test-battery-collection.js

require('dotenv').config();

function calculateManhattanDistance(bot, target) {
  return Math.abs(target.row - bot.row) + Math.abs(target.col - bot.col);
}

function findBestBattery(botRow, botCol, batteryLocations) {
  let bestBattery = null;
  let bestDistance = Infinity;
  let bestScore = -1; // For tie-breaking
  
  batteryLocations.forEach(battery => {
    const distance = calculateManhattanDistance({row: botRow, col: botCol}, battery);
    
    // Scoring system for tie-breaking:
    // Same column = 3 points, same row = 2 points, lower row = 1 point, lower col = 0.5 points
    let score = 0;
    if (battery.col === botCol) score += 3; // Same column (easier path)
    else if (battery.row === botRow) score += 2; // Same row
    score += (15 - battery.row) * 0.1; // Prefer lower rows (small bonus)
    score += (15 - battery.col) * 0.05; // Prefer lower columns (tiny bonus)
    
    console.log(`Battery at (${battery.row}, ${battery.col}): distance=${distance}, score=${score.toFixed(2)}`);
    
    if (distance < bestDistance || (distance === bestDistance && score > bestScore)) {
      bestBattery = battery;
      bestDistance = distance;
      bestScore = score;
    }
  });
  
  return { battery: bestBattery, distance: bestDistance, score: bestScore };
}

function getDirection(botRow, botCol, targetRow, targetCol) {
  if (targetRow > botRow) return 'down';
  if (targetRow < botRow) return 'up';
  if (targetCol > botCol) return 'right';
  if (targetCol < botCol) return 'left';
  return 'none'; // Already at target
}

function testBatteryCollection() {
  console.log('🔋 Testing Battery Collection Logic\n');
  
  // Test case from user's issue
  const testCase = {
    botRow: 5,
    botCol: 10,
    batteryLocations: [
      { row: 1, col: 6 },
      { row: 13, col: 10 }
    ]
  };
  
  console.log(`Bot position: (${testCase.botRow}, ${testCase.botCol})`);
  console.log('Available batteries:');
  testCase.batteryLocations.forEach((battery, i) => {
    console.log(`  Battery ${i+1}: (${battery.row}, ${battery.col})`);
  });
  console.log();
  
  // Calculate distances
  console.log('Distance calculations:');
  const result = findBestBattery(testCase.botRow, testCase.botCol, testCase.batteryLocations);
  
  console.log(`\nBest target: Battery at (${result.battery.row}, ${result.battery.col})`);
  console.log(`Distance: ${result.distance}`);
  console.log(`Score: ${result.score.toFixed(2)}`);
  
  const direction = getDirection(testCase.botRow, testCase.botCol, result.battery.row, result.battery.col);
  console.log(`Recommended direction: ${direction}`);
  
  // Verify the logic
  console.log('\n📋 Analysis:');
  
  const battery1Distance = calculateManhattanDistance({row: testCase.botRow, col: testCase.botCol}, testCase.batteryLocations[0]);
  const battery2Distance = calculateManhattanDistance({row: testCase.botRow, col: testCase.botCol}, testCase.batteryLocations[1]);
  
  console.log(`Battery 1 (1,6): distance = |1-5| + |6-10| = ${Math.abs(1-5)} + ${Math.abs(6-10)} = ${battery1Distance}`);
  console.log(`Battery 2 (13,10): distance = |13-5| + |10-10| = ${Math.abs(13-5)} + ${Math.abs(10-10)} = ${battery2Distance}`);
  
  if (battery1Distance === battery2Distance) {
    console.log('✅ Equal distances detected');
    if (result.battery.col === testCase.botCol) {
      console.log('✅ Correctly chose battery in same column (easier path)');
    } else {
      console.log('❌ Should have chosen battery in same column');
    }
  }
  
  if (direction === 'down') {
    console.log('✅ Correct direction: down (13 > 5, so increase row number)');
  } else {
    console.log(`❌ Wrong direction: expected 'down', got '${direction}'`);
  }
  
  // Test the wrong case that was happening
  console.log('\n🚫 Previous wrong logic would have suggested:');
  console.log('Target: Battery 1 at (1,6) - distance 8');
  console.log('Direction to (1,6) from (5,10): up and left');
  console.log('But AI suggested "right" - which is completely wrong!');
  
  console.log('\n✅ Correct logic now suggests:');
  console.log('Target: Battery 2 at (13,10) - distance 8, same column');
  console.log('Direction to (13,10) from (5,10): down');
  console.log('This makes much more sense!');
}

testBatteryCollection();
