// Test the final tool-based approach
// Run with: node test-final-tool-approach.js

require('dotenv').config();

async function testToolBasedApproach() {
  console.log('🔧 Testing Final Tool-Based Approach\n');

  // Test the exact scenario from the user's log
  const gameState = {
    grid: Array(15).fill().map(() => Array(15).fill(' ')),
    botRow: 7,
    botCol: 10,
    objective: "collect all batteries",
    turtleLocations: [
      { row: 5, col: 6 },
      { row: 7, col: 11 },
      { row: 8, col: 1 },
      { row: 11, col: 8 },
      { row: 12, col: 8 }
    ],
    batteryLocations: [
      { row: 5, col: 8 },   // Distance: |5-7| + |8-10| = 2 + 2 = 4
      { row: 8, col: 13 }   // Distance: |8-7| + |13-10| = 1 + 3 = 4
    ]
  };

  // Set up the grid
  gameState.grid[7][10] = '🤖';
  gameState.grid[5][8] = '🔋';
  gameState.grid[8][13] = '🔋';
  gameState.turtleLocations.forEach(turtle => {
    gameState.grid[turtle.row][turtle.col] = '🐢';
  });

  console.log('🎮 Game State:');
  console.log(`Bot: (${gameState.botRow}, ${gameState.botCol})`);
  console.log('Batteries:', gameState.batteryLocations.map(b => `(${b.row}, ${b.col})`).join(', '));
  console.log('Turtles:', gameState.turtleLocations.map(t => `(${t.row}, ${t.col})`).join(', '));
  console.log();

  // Test the tool logic manually
  console.log('🔧 Tool Analysis:');
  
  // Calculate distances
  const distances = gameState.batteryLocations.map(battery => ({
    battery,
    distance: Math.abs(battery.row - gameState.botRow) + Math.abs(battery.col - gameState.botCol)
  }));
  
  console.log('Distances:');
  distances.forEach((item, i) => {
    console.log(`  Battery ${i+1} at (${item.battery.row}, ${item.battery.col}): distance ${item.distance}`);
  });

  // Find best target with scoring
  const scored = distances.map(item => {
    let score = 0;
    
    // Prefer closer targets
    score += (20 - item.distance);
    
    // Prefer same row or column (easier path)
    if (item.battery.row === gameState.botRow) score += 10; // Same row
    if (item.battery.col === gameState.botCol) score += 15; // Same column
    
    // Penalize targets near obstacles
    const nearObstacles = gameState.turtleLocations.filter(turtle => 
      Math.abs(turtle.row - item.battery.row) <= 1 && 
      Math.abs(turtle.col - item.battery.col) <= 1
    ).length;
    score -= nearObstacles * 3;
    
    return { ...item, score };
  });
  
  scored.sort((a, b) => b.score - a.score);
  const bestTarget = scored[0];
  
  console.log('\nTarget Scoring:');
  scored.forEach((item, i) => {
    const sameRow = item.battery.row === gameState.botRow ? ' (same row +10)' : '';
    const sameCol = item.battery.col === gameState.botCol ? ' (same col +15)' : '';
    console.log(`  Battery ${i+1}: score ${item.score}${sameRow}${sameCol}`);
  });
  
  console.log(`\nBest target: (${bestTarget.battery.row}, ${bestTarget.battery.col}) with score ${bestTarget.score}`);

  // Get next move
  let direction;
  if (bestTarget.battery.row > gameState.botRow) direction = 'down';
  else if (bestTarget.battery.row < gameState.botRow) direction = 'up';
  else if (bestTarget.battery.col > gameState.botCol) direction = 'right';
  else if (bestTarget.battery.col < gameState.botCol) direction = 'left';
  else direction = 'none';

  console.log(`\nNext move: ${direction}`);
  console.log(`Reasoning: Target row ${bestTarget.battery.row} ${bestTarget.battery.row < gameState.botRow ? '<' : '>'} bot row ${gameState.botRow}, so move ${direction}`);

  // Compare with the old wrong result
  console.log('\n📊 Comparison:');
  console.log('❌ Old approach said: "right" (completely wrong!)');
  console.log(`✅ Tool approach says: "${direction}" (correct!)`);
  
  if (direction === 'up') {
    console.log('✅ This is correct because battery at (5,8) is UP from bot at (7,10)');
  } else {
    console.log('❌ Expected "up" direction');
  }

  console.log('\n🎯 Expected Behavior:');
  console.log('1. Both batteries have equal distance (4)');
  console.log('2. Neither is in same row/column, so scoring is based on obstacles');
  console.log('3. Should choose the safer target');
  console.log('4. Move UP to reach battery at (5,8)');
  console.log('5. No more loops or wrong directions!');
}

if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_BASE) {
  console.log('✅ API configured - tool-based approach should work in game');
} else {
  console.log('⚠️  API not configured, but tool logic can still be tested');
}

testToolBasedApproach();
