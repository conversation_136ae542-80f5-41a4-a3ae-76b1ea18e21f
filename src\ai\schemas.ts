/**
 * @fileOverview Shared Zod schemas for AI flows.
 */

import { z } from 'zod';

export const SuggestLaserDirectionInputSchema = z.object({
  grid: z
    .array(z.array(z.string()))
    .describe('A 2D array representing the game grid.  Use emoji for characters.'),
  botRow: z.number().describe('The row index of the bot on the grid.'),
  botCol: z.number().describe('The column index of the bot on the grid.'),
});


const LocationSchema = z.object({
  row: z.number(),
  col: z.number(),
});

export const SuggestMoveDirectionInputSchema = z.object({
  grid: z
    .array(z.array(z.string()))
    .describe('A 2D array representing the game grid.  Use emoji for characters.'),
  botRow: z.number().describe('The row index of the bot on the grid.'),
  botCol: z.number().describe('The column index of the bot on the grid.'),
  objective: z.string().describe('The high-level objective for the bot.'),
  turtleLocations: z.array(LocationSchema).optional().describe('The locations of all turtles on the grid.'),
  batteryLocations: z.array(LocationSchema).optional().describe('The locations of all batteries on the grid.'),
});
