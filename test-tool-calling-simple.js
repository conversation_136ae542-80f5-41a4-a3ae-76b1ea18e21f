/**
 * Simple test to verify tool calling is working
 */

const { config } = require('dotenv');
const OpenAI = require('openai');

// Load environment variables
config();

// Simple game state for testing
const gameState = {
  grid: [
    ['🟫', '🟫', '🟫', '🟫', '🟫'],
    ['🟫', '⬜', '🔋', '⬜', '🟫'],
    ['🟫', '⬜', '🤖', '⬜', '🟫'],
    ['🟫', '⬜', '🔋', '⬜', '🟫'],
    ['🟫', '🟫', '🟫', '🟫', '🟫']
  ],
  botRow: 2,
  botCol: 2,
  objective: 'collect_batteries',
  batteryLocations: [
    { row: 1, col: 2 },
    { row: 3, col: 2 }
  ],
  turtleLocations: []
};

// Simple tool function for testing
function calculateDistances(args) {
  const { botRow, botCol, targets } = args;
  console.log(`🔧 calculateDistances called with bot at (${botRow}, ${botCol})`);

  return targets.map(target => ({
    target,
    distance: Math.abs(target.row - botRow) + Math.abs(target.col - botCol)
  })).sort((a, b) => a.distance - b.distance);
}

async function testToolCalling() {
  console.log('🧪 Testing Tool Calling Implementation');
  console.log('=====================================');

  console.log('\n🎮 Game State:');
  console.log(`Bot: (${gameState.botRow}, ${gameState.botCol})`);
  console.log('Batteries:', gameState.batteryLocations.map(b => `(${b.row},${b.col})`).join(', '));
  console.log('Objective:', gameState.objective);

  console.log('\n📋 Grid:');
  gameState.grid.forEach(row => console.log(row.join('')));

  if (!process.env.OPENAI_API_KEY || !process.env.OPENAI_API_BASE) {
    console.log('\n⚠️  API credentials not configured. Please set:');
    console.log('   OPENAI_API_KEY=your-codestral-api-key');
    console.log('   OPENAI_API_BASE=your-codestral-api-base-url');
    return;
  }

  // Initialize OpenAI client
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: process.env.OPENAI_API_BASE,
  });

  // Define tools
  const tools = [
    {
      type: "function",
      function: {
        name: "calculate_distances",
        description: "Calculate Manhattan distances from bot to targets",
        parameters: {
          type: "object",
          properties: {
            botRow: { type: "number" },
            botCol: { type: "number" },
            targets: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  row: { type: "number" },
                  col: { type: "number" }
                }
              }
            }
          },
          required: ["botRow", "botCol", "targets"]
        }
      }
    }
  ];

  const messages = [
    {
      role: 'system',
      content: 'You are a game AI. Use the available tools to analyze the situation.'
    },
    {
      role: 'user',
      content: `Analyze this game state:
Bot: (${gameState.botRow}, ${gameState.botCol})
Batteries: ${gameState.batteryLocations.map(b => `(${b.row},${b.col})`).join(', ')}

Use the calculate_distances tool to find distances to batteries, then recommend the best move.`
    }
  ];

  try {
    console.log('\n🔧 Making OpenAI API call with tools...');
    const startTime = Date.now();

    // First API call
    const completion = await openai.chat.completions.create({
      model: 'codestral-latest',
      messages: messages,
      tools: tools,
      tool_choice: 'auto',
      temperature: 0.1,
    });

    const responseMessage = completion.choices[0].message;
    console.log('\n🤖 LLM Response:', responseMessage.content || 'Calling tools...');

    if (responseMessage.tool_calls) {
      console.log(`\n📞 LLM wants to call ${responseMessage.tool_calls.length} tools:`);

      for (const toolCall of responseMessage.tool_calls) {
        const functionName = toolCall.function.name;
        const args = JSON.parse(toolCall.function.arguments);

        console.log(`   • ${functionName}(${JSON.stringify(args)})`);

        if (functionName === 'calculate_distances') {
          const result = calculateDistances(args);
          console.log(`   → Result:`, result);

          // Add tool result to conversation
          messages.push(responseMessage);
          messages.push({
            role: 'tool',
            tool_call_id: toolCall.id,
            name: functionName,
            content: JSON.stringify(result)
          });
        }
      }

      // Second API call to get final response
      const finalCompletion = await openai.chat.completions.create({
        model: 'codestral-latest',
        messages: messages,
        temperature: 0.1,
      });

      const finalResponse = finalCompletion.choices[0].message.content;
      console.log('\n✅ Final Decision:', finalResponse);

      const endTime = Date.now();
      console.log(`\n⏱️  Total time: ${endTime - startTime}ms`);
      console.log('\n✅ SUCCESS: Tool calling is working correctly!');
      console.log('   • Tools parameter was passed to OpenAI API');
      console.log('   • LLM called the calculate_distances function');
      console.log('   • Function was executed and results returned');
      console.log('   • LLM used the results to make a decision');

    } else {
      console.log('\n⚠️  No tool calls made by the LLM');
      console.log('This could mean:');
      console.log('   • Tools parameter not passed correctly');
      console.log('   • LLM decided not to use tools');
      console.log('   • API doesn\'t support function calling');
    }

  } catch (error) {
    console.error('\n❌ Error testing tool calling:', error.message);
    if (error.message.includes('tools')) {
      console.error('This might indicate that your API endpoint doesn\'t support function calling');
    }
  }
}

// Run the test
testToolCalling().catch(console.error);
