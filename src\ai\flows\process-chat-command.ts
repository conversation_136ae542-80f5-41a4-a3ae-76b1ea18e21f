'use server';

/**
 * @fileOverview A function that processes natural language commands for the Emoji Bot Arena game.
 *
 * - processChatCommand - A function that interprets a chat message and returns a game command.
 * - ProcessChatCommandInput - The input type for the processChatCommand function.
 * - ProcessChatCommandOutput - The return type for the processChatCommand function.
 */

import { openai, defaultModel } from '@/ai/openai-client';
import { z } from 'zod';

const LocationSchema = z.object({
  row: z.number(),
  col: z.number(),
});

const ProcessChatCommandInputSchema = z.object({
  command: z.string().describe('The natural language command from the user.'),
  grid: z
    .array(z.array(z.string()))
    .describe('A 2D array representing the game grid.'),
  botRow: z.number().describe('The row index of the bot on the grid.'),
  botCol: z.number().describe('The column index of the bot on the grid.'),
  turtleLocations: z.array(LocationSchema).describe('The locations of all turtles on the grid.'),
  batteryLocations: z.array(LocationSchema).describe('The locations of all batteries on the grid.'),
});
export type ProcessChatCommandInput = z.infer<
  typeof ProcessChatCommandInputSchema
>;

const ActionSchema = z.object({
  action: z
    .enum(['move', 'fire', 'unknown'])
    .describe("The game action to perform."),
  direction: z
    .enum(['up', 'down', 'left', 'right', 'none'])
    .describe("The direction for the action."),
});

const ProcessChatCommandOutputSchema = z.object({
  actions: z.array(ActionSchema).describe("A sequence of game actions to perform. This should be empty if a high-level mission is identified."),
  response: z.string().describe('A natural language response to the user about the action being taken.'),
  mission: z.enum(['collect_batteries', 'eliminate_turtles', 'none']).optional().describe("The high-level mission identified from the command. If a mission is found, 'actions' should be empty. Set to 'none' for simple, direct commands."),
  toolLog: z.string().nullable().optional().describe('A log of any tool calls made, including the tool name and key arguments.'),
  reasoning: z.string().nullable().optional().describe("The reasoning provided by the tool for its suggestion."),
});
export type ProcessChatCommandOutput = z.infer<
  typeof ProcessChatCommandOutputSchema
>;

export async function processChatCommand(
  input: ProcessChatCommandInput
): Promise<ProcessChatCommandOutput> {
  console.log('🎮 Processing command:', input.command);

  // Simple fallback for common mission phrases
  const command = input.command.toLowerCase();
  console.log('🔍 Checking for mission patterns in:', command);
  if (command.includes('get all batteries') ||
      command.includes('collect all batteries') ||
      command.includes('collect all the batteries') ||
      command.includes('get every battery')) {
    console.log('🎯 Direct mission detection: collect_batteries');
    return {
      actions: [],
      response: "Roger that. Starting mission: collect all batteries.",
      mission: 'collect_batteries',
      toolLog: 'Direct mission detection',
      reasoning: 'Command matched battery collection pattern'
    };
  }

  if (command.includes('kill all turtles') ||
      command.includes('eliminate all turtles') ||
      command.includes('destroy all turtles') ||
      command.includes('clear all turtles') ||
      command.includes('kill all turles') || // typo variation
      command.includes('kill al turles') ||  // typo variation
      command.includes('kill al turtles')) {
    console.log('🎯 Direct mission detection: eliminate_turtles');
    return {
      actions: [],
      response: "Roger that. Starting mission: eliminate all turtles.",
      mission: 'eliminate_turtles',
      toolLog: 'Direct mission detection',
      reasoning: 'Command matched turtle elimination pattern'
    };
  }

  if (command.includes('kill turtle') ||
      command.includes('destroy turtle') ||
      command.includes('eliminate turtle')) {
    console.log('🎯 Direct mission detection: eliminate_turtles (single target)');
    return {
      actions: [],
      response: "Roger that. Hunting down turtles!",
      mission: 'eliminate_turtles',
      toolLog: 'Direct mission detection - single turtle',
      reasoning: 'Command matched single turtle elimination pattern'
    };
  }

  const gridDisplay = input.grid.map(row => row.join('')).join('\n');

  let turtleInfo = 'None';
  if (input.turtleLocations && input.turtleLocations.length > 0) {
    turtleInfo = input.turtleLocations.map(loc => `  - (row: ${loc.row}, col: ${loc.col})`).join('\n');
  }

  let batteryInfo = 'None';
  if (input.batteryLocations && input.batteryLocations.length > 0) {
    batteryInfo = input.batteryLocations.map(loc => `  - (row: ${loc.row}, col: ${loc.col})`).join('\n');
  }

  const prompt = `You are the AI controlling the bot in the Emoji Bot Arena game. Your task is to interpret a user's natural language command and translate it into a structured command for the game.

**Primary Task: Classify the Command**

You must first determine if the command is a **Simple Action** or a **High-Level Mission**.

1.  **Simple Actions**:
    *   **Description**: These are direct, explicit instructions that can be translated into one or more basic game actions immediately.
    *   **Examples**: "go left", "shoot up", "move left 3 units", "go up and then shoot right", "go to row 10, column 5".
    *   **Your Output**:
        *   Generate a complete sequence of one or more action objects in the 'actions' array.
        *   Set the \`mission\` field to "none" or omit it entirely.
        *   Provide a suitable \`response\`, e.g., "Okay, moving left three times."
    *   **Tool Use**: For vague fire commands like "destroy the turtle" or "fire laser", you should determine the optimal fire direction based on turtle positions.

2.  **High-Level Missions**:
    *   **Description**: These are broad, objective-based commands that require multiple steps to complete, like collecting all items of a certain type.
    *   **Examples**: "collect all the batteries", "get every battery", "eliminate all turtles", "clear the board of turtles".
    *   **Your Output**:
        *   You MUST NOT generate the step-by-step actions yourself.
        *   The 'actions' array MUST be empty.
        *   Identify the mission and set the \`mission\` field to either \`"collect_batteries"\` or \`"eliminate_turtles"\`.
        *   Provide a confirmation message in the \`response\` field, e.g., "Roger that. Starting mission: collect all batteries."

3.  **Unclear Commands**:
    *   If you cannot understand the command, set \`actions\` to \`[{ action: 'unknown', direction: 'none' }]\` and provide a clarifying \`response\`.

**Grid Coordinate System:**
- The grid's origin (row: 0, col: 0) is at the **TOP-LEFT** corner.
- Moving **UP** DECREASES the row index.
- Moving **DOWN** INCREASES the row index.
- Moving **LEFT** DECREASES the column index.
- Moving **RIGHT** INCREASES the column index.

**Game Context (Source of Truth):**
- User command: "${input.command}"
- Bot: (row: ${input.botRow}, col: ${input.botCol})
- Turtles:
${turtleInfo}
- Batteries:
${batteryInfo}
- Grid (for visual context):
${gridDisplay}

**For fire commands:**
When the user asks to fire, shoot, or destroy something:
1. **Check if target is specified**: Look for row/column coordinates in the command
2. **Validate line of sight**: Bot can only hit targets on same row or column
3. **If specific target mentioned**:
   - Check if target is at (row, col) mentioned in command
   - Verify if bot can actually hit that position (same row or column)
   - If not reachable, explain why and suggest movement instead
4. **If no specific target**: Analyze all turtle positions and suggest best direction
5. **Consider obstacles**: Check for batteries blocking the path

**VERY IMPORTANT JSON FORMATTING RULES:**
Your entire response MUST be a single, valid JSON object. Do not include any other text, explanations, or markdown formatting.
An action object must always look like this: \`{ "action": "move", "direction": "up" }\`.
NEVER swap the values. For example, \`{ "action": "up", "direction": "move" }\` is INVALID and will fail.
The only valid values for 'action' are "move", "fire", and "unknown". Collecting a battery is an automatic result of moving onto its square.

Respond with a JSON object in this exact format:
{
  "actions": [{"action": "move", "direction": "up"}],
  "response": "Moving up",
  "mission": "none",
  "toolLog": null,
  "reasoning": null
}`;

  // Handle fire commands with specific target validation (but NOT mission commands)
  if ((input.command.toLowerCase().includes('fire') ||
       input.command.toLowerCase().includes('shoot') ||
       input.command.toLowerCase().includes('destroy') ||
       input.command.toLowerCase().includes('kill')) &&
      // EXCLUDE mission commands that should go to mission system
      !input.command.toLowerCase().includes('all') &&
      !input.command.toLowerCase().includes('every')) {

    // Check if specific coordinates are mentioned
    const rowMatch = input.command.match(/row\s+(\d+)/i);
    const colMatch = input.command.match(/col(?:umn)?\s+(\d+)/i);

    if (rowMatch && colMatch) {
      const targetRow = parseInt(rowMatch[1]);
      const targetCol = parseInt(colMatch[1]);

      // Check if target is actually a turtle
      const targetIsTurtle = input.turtleLocations?.some(turtle =>
        turtle.row === targetRow && turtle.col === targetCol
      );

      if (!targetIsTurtle) {
        return {
          actions: [],
          response: `There is no turtle at row ${targetRow}, column ${targetCol}.`,
          mission: 'none',
          toolLog: null,
          reasoning: null
        };
      }

      // Check if target is reachable (same row or column)
      const sameRow = targetRow === input.botRow;
      const sameCol = targetCol === input.botCol;

      if (!sameRow && !sameCol) {
        return {
          actions: [],
          response: `Cannot fire at turtle at (${targetRow}, ${targetCol}). Bot at (${input.botRow}, ${input.botCol}) can only fire in straight lines. The turtle is not on the same row or column.`,
          mission: 'none',
          toolLog: null,
          reasoning: null
        };
      }

      // Determine direction and check for obstacles
      let direction: 'up' | 'down' | 'left' | 'right';
      if (sameRow) {
        direction = targetCol < input.botCol ? 'left' : 'right';
      } else {
        direction = targetRow < input.botRow ? 'up' : 'down';
      }

      return {
        actions: [{ action: 'fire', direction }],
        response: `Firing ${direction} at turtle at row ${targetRow}, column ${targetCol}.`,
        mission: 'none',
        toolLog: `Direct target fire at (${targetRow}, ${targetCol})`,
        reasoning: `Target is reachable via ${direction} direction`
      };
    }

    // Check if direction is not specified (general fire command)
    const hasDirection = ['up', 'down', 'left', 'right'].some(dir =>
      input.command.toLowerCase().includes(dir)
    );

    if (!hasDirection && input.turtleLocations && input.turtleLocations.length > 0) {
      // Simple deterministic laser direction logic
      const botRow = input.botRow;
      const botCol = input.botCol;

      // Check each direction for turtles in line of sight
      const directions = ['up', 'down', 'left', 'right'] as const;

      for (const direction of directions) {
        const turtlesInDirection = input.turtleLocations.filter(turtle => {
          if (direction === 'up') return turtle.col === botCol && turtle.row < botRow;
          if (direction === 'down') return turtle.col === botCol && turtle.row > botRow;
          if (direction === 'left') return turtle.row === botRow && turtle.col < botCol;
          if (direction === 'right') return turtle.row === botRow && turtle.col > botCol;
          return false;
        });

        if (turtlesInDirection.length > 0) {
          const closestTurtle = turtlesInDirection.reduce((closest, turtle) => {
            const currentDistance = Math.abs(turtle.row - botRow) + Math.abs(turtle.col - botCol);
            const closestDistance = Math.abs(closest.row - botRow) + Math.abs(closest.col - botCol);
            return currentDistance < closestDistance ? turtle : closest;
          });

          return {
            actions: [{ action: 'fire', direction }],
            response: `Firing ${direction} at turtle at (${closestTurtle.row}, ${closestTurtle.col}).`,
            mission: 'none',
            toolLog: `Direct calculation: found turtle in ${direction} direction`,
            reasoning: `Turtle at (${closestTurtle.row}, ${closestTurtle.col}) is in line of sight ${direction}`
          };
        }
      }

      // No turtles in line of sight, fire at closest turtle's general direction
      if (input.turtleLocations.length > 0) {
        const closestTurtle = input.turtleLocations.reduce((closest, turtle) => {
          const currentDistance = Math.abs(turtle.row - botRow) + Math.abs(turtle.col - botCol);
          const closestDistance = Math.abs(closest.row - botRow) + Math.abs(closest.col - botCol);
          return currentDistance < closestDistance ? turtle : closest;
        });

        // Choose direction that gets us closer to the turtle
        let direction: 'up' | 'down' | 'left' | 'right' = 'up';
        const rowDiff = closestTurtle.row - botRow;
        const colDiff = closestTurtle.col - botCol;

        if (Math.abs(rowDiff) > Math.abs(colDiff)) {
          direction = rowDiff > 0 ? 'down' : 'up';
        } else {
          direction = colDiff > 0 ? 'right' : 'left';
        }

        return {
          actions: [{ action: 'fire', direction }],
          response: `Firing ${direction} toward turtle at (${closestTurtle.row}, ${closestTurtle.col}).`,
          mission: 'none',
          toolLog: `Direct calculation: firing toward closest turtle`,
          reasoning: `No direct line of sight, firing ${direction} toward closest turtle`
        };
      }
    }
  }

  const completion = await openai.chat.completions.create({
    model: defaultModel,
    messages: [{ role: 'user', content: prompt }],
    temperature: 0.1,
    response_format: { type: 'json_object' },
    
  });

  const content = completion.choices[0]?.message?.content;
  if (!content) {
    throw new Error('No response content received from OpenAI');
  }

  let result: ProcessChatCommandOutput;
  try {
    const parsed = JSON.parse(content);
    result = ProcessChatCommandOutputSchema.parse(parsed);
  } catch (error) {
    console.error('Failed to parse or validate response:', error);
    console.error('Raw response:', content);
    throw new Error(`Invalid response format: ${error}`);
  }

  console.log('🤖 AI Response:', {
    mission: result.mission,
    response: result.response,
    actionsCount: result.actions.length
  });

  return result;
}


