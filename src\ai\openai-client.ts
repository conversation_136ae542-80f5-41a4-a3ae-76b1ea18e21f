import OpenAI from 'openai';

// Initialize OpenAI client with custom base URL and API key
export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_API_BASE,
});

// Default model - you can change this to match your Codestral model name
export const defaultModel = 'codestral-latest';

export const availableModels = [defaultModel];

// Tool calling approach - OpenAI function calling standard
export interface ToolFunction {
  name: string;
  description: string;
  parameters: any;
  handler: (args: any) => any | Promise<any>;
}

export interface ToolCallResult {
  toolCalls: Array<{
    id: string;
    name: string;
    arguments: any;
    result: any;
  }>;
  finalResponse?: string;
}

export async function createToolCompletion(
  messages: Array<{ role: 'user' | 'assistant' | 'system'; content: string }>,
  tools: ToolFunction[],
  model: string = defaultModel,
  maxIterations: number = 5
): Promise<ToolCallResult> {
  const toolCallResults: ToolCallResult['toolCalls'] = [];
  const conversationMessages: any[] = [...messages];

  // Convert our tools to OpenAI format
  const openaiTools = tools.map(tool => ({
    type: 'function' as const,
    function: {
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters
    }
  }));

  // Create a map for quick tool lookup
  const toolMap = new Map(tools.map(tool => [tool.name, tool]));

  for (let iteration = 0; iteration < maxIterations; iteration++) {
    const completion = await openai.chat.completions.create({
      model,
      messages: conversationMessages,
      tools: openaiTools,
      tool_choice: 'auto',
      temperature: 0.1,
    });

    const responseMessage = completion.choices[0]?.message;
    if (!responseMessage) {
      throw new Error('No response message received from OpenAI');
    }

    // Add the assistant's response to conversation
    conversationMessages.push(responseMessage);

    // Check if the model wants to call tools
    if (responseMessage.tool_calls && responseMessage.tool_calls.length > 0) {
      // Execute each tool call
      for (const toolCall of responseMessage.tool_calls) {
        const tool = toolMap.get(toolCall.function.name);
        if (!tool) {
          throw new Error(`Unknown tool: ${toolCall.function.name}`);
        }

        try {
          const args = JSON.parse(toolCall.function.arguments);
          const result = await tool.handler(args);

          toolCallResults.push({
            id: toolCall.id,
            name: toolCall.function.name,
            arguments: args,
            result
          });

          // Add tool result to conversation
          conversationMessages.push({
            role: 'tool',
            tool_call_id: toolCall.id,
            name: toolCall.function.name,
            content: JSON.stringify(result)
          });
        } catch (error) {
          console.error(`Error executing tool ${toolCall.function.name}:`, error);
          // Add error result to conversation
          conversationMessages.push({
            role: 'tool',
            tool_call_id: toolCall.id,
            name: toolCall.function.name,
            content: JSON.stringify({ error: `Tool execution failed: ${error}` })
          });
        }
      }
    } else {
      // No more tool calls, we have the final response
      return {
        toolCalls: toolCallResults,
        finalResponse: responseMessage.content || undefined
      };
    }
  }

  // If we've reached max iterations, return what we have
  return {
    toolCalls: toolCallResults,
    finalResponse: 'Maximum iterations reached'
  };
}
