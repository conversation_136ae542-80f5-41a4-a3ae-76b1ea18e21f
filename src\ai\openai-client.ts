import OpenAI from 'openai';

// Initialize OpenAI client with custom base URL and API key
export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_API_BASE,
});

// Default model - you can change this to match your Codestral model name
export const defaultModel = 'codestral-latest';

export const availableModels = [defaultModel];

// Helper function to create structured completions with schema validation
export async function createStructuredCompletion<T>(
  prompt: string,
  schema: any,
  model: string = defaultModel
): Promise<T> {
  const completion = await openai.chat.completions.create({
    model,
    messages: [
      {
        role: 'user',
        content: prompt,
      },
    ],
    temperature: 0.1,
    response_format: { type: 'json_object' },
  });

  const content = completion.choices[0]?.message?.content;
  if (!content) {
    throw new Error('No response content received from OpenAI');
  }

  try {
    const parsed = JSON.parse(content);
    return schema.parse(parsed);
  } catch (error) {
    console.error('Failed to parse or validate response:', error);
    console.error('Raw response:', content);
    throw new Error(`Invalid response format: ${error}`);
  }
}
