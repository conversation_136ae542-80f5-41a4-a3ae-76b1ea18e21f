'use client';

import { useState, useRef, useEffect } from 'react';
import type { ChatMessage } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Send } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChatBoxProps {
    messages: ChatMessage[];
    onSendMessage: (message: string) => void;
    disabled: boolean;
    isProcessing: boolean;
}

export function ChatBox({ messages, onSendMessage, disabled, isProcessing }: ChatBoxProps) {
    const [input, setInput] = useState('');
    const messagesEndRef = useRef<HTMLDivElement>(null);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (input.trim() && !disabled && !isProcessing) {
            onSendMessage(input.trim());
            setInput('');
        }
    };

    return (
        <div className="space-y-2">
            <h3 className="font-semibold font-headline">Chat Command</h3>
            <ScrollArea className="h-40 w-full rounded-md border p-2">
                <div className="space-y-2">
                    {messages.map((msg, index) => (
                        <div
                            key={index}
                            className={cn(
                                "flex items-start gap-2 text-sm",
                                msg.sender === 'user' ? 'justify-end' : 'justify-start'
                            )}
                        >
                            <p className={cn(
                                "max-w-[85%] rounded-lg px-3 py-2",
                                msg.sender === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'
                            )}>
                                {msg.message}
                            </p>
                        </div>
                    ))}
                    <div ref={messagesEndRef} />
                 </div>
            </ScrollArea>
            <form onSubmit={handleSubmit} className="flex gap-2">
                <Input
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    placeholder="e.g. move left"
                    disabled={disabled || isProcessing}
                />
                <Button type="submit" disabled={disabled || isProcessing} size="icon">
                    {isProcessing ? "..." : <Send className="h-4 w-4"/>}
                </Button>
            </form>
        </div>
    );
}
