import type {Metadata} from 'next';
import './globals.css';
import { Toaster } from "@/components/ui/toaster";

export const metadata: Metadata = {
  title: 'DevX STUDIO - Cyberpunk Bot Arena',
  description: 'A cyberpunk-themed web game where an AI bot battles digital entities in a neural grid.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet" />
      </head>
      <body className="font-body antialiased min-h-screen overflow-x-hidden">
        <div className="relative min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          {/* Animated cyberpunk background */}
          <div className="fixed inset-0 overflow-hidden pointer-events-none">
            {/* Main border scan lines */}
            <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent animate-pulse"></div>
            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-purple-400/50 to-transparent animate-pulse"></div>
            <div className="absolute top-0 left-0 h-full w-0.5 bg-gradient-to-b from-transparent via-pink-400/50 to-transparent animate-pulse"></div>
            <div className="absolute top-0 right-0 h-full w-0.5 bg-gradient-to-b from-transparent via-cyan-400/50 to-transparent animate-pulse"></div>

            {/* Floating particles */}
            <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-cyan-400/60 rounded-full animate-ping" style={{ animationDelay: '0s' }}></div>
            <div className="absolute top-1/3 right-1/3 w-0.5 h-0.5 bg-purple-400/60 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
            <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-pink-400/60 rounded-full animate-bounce" style={{ animationDelay: '2s' }}></div>
            <div className="absolute top-2/3 right-1/4 w-0.5 h-0.5 bg-green-400/60 rounded-full animate-ping" style={{ animationDelay: '3s' }}></div>
            <div className="absolute bottom-1/3 right-2/3 w-1 h-1 bg-yellow-400/60 rounded-full animate-pulse" style={{ animationDelay: '4s' }}></div>

            {/* Moving data streams */}
            <div className="absolute top-0 left-1/4 w-0.5 h-full bg-gradient-to-b from-transparent via-cyan-400/20 to-transparent data-stream" style={{ animationDelay: '0s' }}></div>
            <div className="absolute top-0 right-1/3 w-0.5 h-full bg-gradient-to-b from-transparent via-purple-400/20 to-transparent data-stream" style={{ animationDelay: '2s' }}></div>
            <div className="absolute top-0 left-2/3 w-0.5 h-full bg-gradient-to-b from-transparent via-pink-400/20 to-transparent data-stream" style={{ animationDelay: '4s' }}></div>

            {/* Grid overlay */}
            <div className="absolute inset-0 opacity-5">
              <div className="grid grid-cols-20 grid-rows-20 h-full w-full">
                {Array.from({ length: 400 }).map((_, i) => (
                  <div key={i} className="border border-cyan-400/30 animate-pulse" style={{ animationDelay: `${(i % 20) * 0.1}s` }}></div>
                ))}
              </div>
            </div>

            {/* Scanning line effect */}
            <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-cyan-400/80 to-transparent scan-line"></div>

            {/* Corner glow effects */}
            <div className="absolute top-0 left-0 w-20 h-20 bg-cyan-400/10 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute top-0 right-0 w-20 h-20 bg-purple-400/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
            <div className="absolute bottom-0 left-0 w-20 h-20 bg-pink-400/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
            <div className="absolute bottom-0 right-0 w-20 h-20 bg-green-400/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '3s' }}></div>
          </div>

          {/* Main content */}
          <div className="relative z-10">
            {children}
          </div>
        </div>
        <Toaster />
      </body>
    </html>
  );
}
