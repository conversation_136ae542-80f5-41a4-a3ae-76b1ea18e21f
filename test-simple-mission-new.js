// Test the simple mission planner (no function calling)
// Run with: node test-simple-mission-new.js

// Test the calculation logic without API calls
function calculateDistance(from, to) {
  return Math.abs(to.row - from.row) + Math.abs(to.col - from.col);
}

function findBestTarget(botRow, botCol, targets, obstacles = []) {
  const analyzed = targets.map(target => {
    const distance = calculateDistance({row: botRow, col: botCol}, target);
    
    let score = 20 - distance; // Base score from distance
    
    // Bonus for same row/column (easier path)
    if (target.row === botRow) score += 10; // Same row
    if (target.col === botCol) score += 15; // Same column (even better)
    
    // Penalty for nearby obstacles
    const nearbyObstacles = obstacles.filter(obs => 
      Math.abs(obs.row - target.row) <= 1 && Math.abs(obs.col - target.col) <= 1
    ).length;
    score -= nearbyObstacles * 3;
    
    return { target, distance, score };
  });
  
  analyzed.sort((a, b) => b.score - a.score);
  return analyzed[0];
}

function getDirectionToTarget(botRow, botCol, targetRow, targetCol) {
  if (targetRow > botRow) return 'down';
  if (targetRow < botRow) return 'up';
  if (targetCol > botCol) return 'right';
  if (targetCol < botCol) return 'left';
  return 'none';
}

function testSimpleMissionLogic() {
  console.log('🔧 Testing Simple Mission Logic (No Function Calling)\n');

  // Test case from user's error log
  const gameState = {
    botRow: 7,
    botCol: 10,
    objective: "collect all batteries",
    batteryLocations: [
      { row: 5, col: 8 },   // Distance: |5-7| + |8-10| = 2 + 2 = 4
      { row: 8, col: 13 }   // Distance: |8-7| + |13-10| = 1 + 3 = 4
    ],
    turtleLocations: [
      { row: 5, col: 6 },
      { row: 7, col: 11 },
      { row: 8, col: 1 },
      { row: 11, col: 8 },
      { row: 12, col: 8 }
    ]
  };

  console.log('🎮 Game State:');
  console.log(`Bot: (${gameState.botRow}, ${gameState.botCol})`);
  console.log('Batteries:', gameState.batteryLocations.map(b => `(${b.row}, ${b.col})`).join(', '));
  console.log('Turtles:', gameState.turtleLocations.map(t => `(${t.row}, ${t.col})`).join(', '));
  console.log();

  // Calculate distances
  const distances = gameState.batteryLocations.map(battery => ({
    battery,
    distance: calculateDistance({row: gameState.botRow, col: gameState.botCol}, battery)
  }));
  
  console.log('📏 Distance Calculations:');
  distances.forEach((d, i) => {
    console.log(`  Battery ${i+1} at (${d.battery.row},${d.battery.col}): distance ${d.distance}`);
  });
  console.log();

  // Find best target
  const best = findBestTarget(gameState.botRow, gameState.botCol, gameState.batteryLocations, gameState.turtleLocations);
  
  console.log('🎯 Target Analysis:');
  console.log(`  Selected: Battery at (${best.target.row}, ${best.target.col})`);
  console.log(`  Distance: ${best.distance}`);
  console.log(`  Score: ${best.score}`);
  console.log();

  // Get direction
  const direction = getDirectionToTarget(gameState.botRow, gameState.botCol, best.target.row, best.target.col);
  
  console.log('🧭 Direction Calculation:');
  console.log(`  Bot row: ${gameState.botRow}, Target row: ${best.target.row}`);
  console.log(`  Bot col: ${gameState.botCol}, Target col: ${best.target.col}`);
  console.log(`  Direction: ${direction}`);
  console.log(`  Logic: ${best.target.row < gameState.botRow ? 'Target row < Bot row, so UP' : best.target.row > gameState.botRow ? 'Target row > Bot row, so DOWN' : 'Same row, check columns'}`);
  console.log();

  // Compare with the error case
  console.log('📊 Comparison with Previous Error:');
  console.log('❌ Previous error: Bot suggested "right" (wrong!)');
  console.log(`✅ Simple logic: Bot suggests "${direction}" (correct!)`);
  
  if (direction === 'up') {
    console.log('✅ This is correct because target (5,8) is UP from bot (7,10)');
  } else {
    console.log('❌ Expected "up" direction');
  }

  console.log('\n🚀 Advantages of Simple Approach:');
  console.log('✅ No function calling - works with any OpenAI-compatible API');
  console.log('✅ Pre-calculated logic reduces API complexity');
  console.log('✅ LLM only validates/improves the decision');
  console.log('✅ Deterministic calculations prevent errors');
  console.log('✅ Compatible with Mistral/Codestral APIs');

  console.log('\n🔧 How It Works:');
  console.log('1. Calculate distances and scores locally');
  console.log('2. Find optimal target using scoring algorithm');
  console.log('3. Calculate direction using simple math');
  console.log('4. Ask LLM to validate the decision');
  console.log('5. Return the final action');
}

testSimpleMissionLogic();
