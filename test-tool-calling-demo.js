/**
 * Simple demonstration of Tool Calling vs Structured Output
 * This shows the conceptual difference without complex imports
 */

const { config } = require('dotenv');
const OpenAI = require('openai');

// Load environment variables
config();

// Initialize OpenAI client (same as your setup)
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_API_BASE,
});

// Mock game state
const gameState = {
  botRow: 4,
  botCol: 2,
  batteryLocations: [
    { row: 2, col: 2 },
    { row: 2, col: 5 },
    { row: 6, col: 3 }
  ],
  objective: 'collect_batteries'
};

// Tool functions (these would be in your actual implementation)
function calculateDistances(args) {
  const { botRow, botCol, targets } = args;
  console.log(`🔧 calculateDistances called with bot at (${botRow}, ${botCol})`);
  
  return targets.map(target => ({
    target,
    distance: Math.abs(target.row - botRow) + Math.abs(target.col - botCol)
  })).sort((a, b) => a.distance - b.distance);
}

function findBestTarget(args) {
  const { distances } = args;
  console.log(`🔧 findBestTarget called with ${distances.length} targets`);
  
  const best = distances[0];
  return {
    target: best.target,
    distance: best.distance,
    reason: `Closest target at distance ${best.distance}`
  };
}

// Demonstration of Structured Output approach
async function demonstrateStructuredOutput() {
  console.log('\n📄 STRUCTURED OUTPUT APPROACH');
  console.log('=' .repeat(50));
  
  const prompt = `You are a game AI. Given this state, return a JSON object with your decision:

Bot position: (${gameState.botRow}, ${gameState.botCol})
Batteries: ${gameState.batteryLocations.map(b => `(${b.row},${b.col})`).join(', ')}
Objective: ${gameState.objective}

Return JSON with: {"action": "move", "direction": "up|down|left|right", "reason": "explanation"}`;

  try {
    const completion = await openai.chat.completions.create({
      model: 'codestral-latest',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1,
      response_format: { type: 'json_object' },
    });

    const result = JSON.parse(completion.choices[0].message.content);
    console.log('✅ Result:', result);
    console.log('📊 Characteristics:');
    console.log('   • Single API call');
    console.log('   • LLM must calculate distances itself');
    console.log('   • Risk of hallucinated calculations');
    console.log('   • Fast response');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Demonstration of Tool Calling approach
async function demonstrateToolCalling() {
  console.log('\n🔧 TOOL CALLING APPROACH');
  console.log('=' .repeat(50));
  
  const tools = [
    {
      type: "function",
      function: {
        name: "calculate_distances",
        description: "Calculate Manhattan distances from bot to targets",
        parameters: {
          type: "object",
          properties: {
            botRow: { type: "number" },
            botCol: { type: "number" },
            targets: { 
              type: "array",
              items: {
                type: "object",
                properties: {
                  row: { type: "number" },
                  col: { type: "number" }
                }
              }
            }
          },
          required: ["botRow", "botCol", "targets"]
        }
      }
    },
    {
      type: "function",
      function: {
        name: "find_best_target",
        description: "Find the best target from distance calculations",
        parameters: {
          type: "object",
          properties: {
            distances: { type: "array" }
          },
          required: ["distances"]
        }
      }
    }
  ];

  const messages = [
    {
      role: 'system',
      content: 'You are a game AI. Use the available tools to analyze the situation and make decisions.'
    },
    {
      role: 'user',
      content: `Analyze this game state and determine the best action:

Bot position: (${gameState.botRow}, ${gameState.botCol})
Batteries: ${gameState.batteryLocations.map(b => `(${b.row},${b.col})`).join(', ')}
Objective: ${gameState.objective}

Use the tools to calculate distances and find the best target, then recommend an action.`
    }
  ];

  try {
    // First API call - LLM decides to use tools
    let completion = await openai.chat.completions.create({
      model: 'codestral-latest',
      messages: messages,
      tools: tools,
      tool_choice: 'auto',
      temperature: 0.1,
    });

    let responseMessage = completion.choices[0].message;
    messages.push(responseMessage);

    console.log('🤖 LLM Response:', responseMessage.content || 'Calling tools...');

    // Handle tool calls
    if (responseMessage.tool_calls) {
      console.log(`📞 LLM wants to call ${responseMessage.tool_calls.length} tools:`);
      
      for (const toolCall of responseMessage.tool_calls) {
        const functionName = toolCall.function.name;
        const args = JSON.parse(toolCall.function.arguments);
        
        console.log(`   • ${functionName}(${JSON.stringify(args)})`);
        
        let result;
        if (functionName === 'calculate_distances') {
          result = calculateDistances(args);
        } else if (functionName === 'find_best_target') {
          result = findBestTarget(args);
        }
        
        console.log(`   → Result:`, result);
        
        // Add tool result to conversation
        messages.push({
          role: 'tool',
          tool_call_id: toolCall.id,
          name: functionName,
          content: JSON.stringify(result)
        });
      }

      // Second API call - get final response with tool results
      completion = await openai.chat.completions.create({
        model: 'codestral-latest',
        messages: messages,
        temperature: 0.1,
      });

      const finalResponse = completion.choices[0].message.content;
      console.log('✅ Final Decision:', finalResponse);
    }
    
    console.log('📊 Characteristics:');
    console.log('   • Multiple API calls (conversation)');
    console.log('   • LLM calls actual functions');
    console.log('   • Real calculations, no hallucination');
    console.log('   • More complex but more accurate');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

async function runDemo() {
  console.log('🎮 TOOL CALLING vs STRUCTURED OUTPUT DEMO');
  console.log('🎯 Game State:');
  console.log(`   Bot: (${gameState.botRow}, ${gameState.botCol})`);
  console.log(`   Batteries: ${gameState.batteryLocations.map(b => `(${b.row},${b.col})`).join(', ')}`);
  console.log(`   Objective: ${gameState.objective}`);

  await demonstrateStructuredOutput();
  await demonstrateToolCalling();

  console.log('\n🎯 SUMMARY:');
  console.log('Tool Calling is better for your use case because:');
  console.log('• Uses OpenAI function calling standard (your preference)');
  console.log('• LLM can call real calculation functions');
  console.log('• Can generate complete command sequences by chaining tools');
  console.log('• No hallucinated math - uses actual functions');
  console.log('• More flexible for complex mission planning');
}

// Check if API credentials are configured
if (!process.env.OPENAI_API_KEY || !process.env.OPENAI_API_BASE) {
  console.log('⚠️  Please configure OPENAI_API_KEY and OPENAI_API_BASE in your .env file');
  console.log('This demo shows the conceptual difference between approaches.');
  console.log('');
  console.log('STRUCTURED OUTPUT: Single call, LLM guesses calculations');
  console.log('TOOL CALLING: Multiple calls, LLM uses real functions');
  console.log('');
  console.log('For your game AI, tool calling is superior because:');
  console.log('1. Matches OpenAI function calling standard');
  console.log('2. No hallucinated calculations');
  console.log('3. Can generate complete command sequences');
  console.log('4. More flexible for complex missions');
} else {
  runDemo().catch(console.error);
}
