'use client';

import { useState, useEffect, useCallback } from 'react';
import { GameGrid } from '@/components/game/game-grid';
import { GameStats } from '@/components/game/game-stats';
import { GameOverScreen } from '@/components/game/game-over-screen';
import type { Grid, Position, Direction, CellValue, ChatMessage, GameAction } from '@/lib/types';
// Removed laser suggestion functionality - now using tool-based mission system


import { executeToolBasedMission } from '@/ai/flows/tool-based-mission';
import { processChatCommand } from '@/ai/flows/process-chat-command';
import { useToast } from "@/hooks/use-toast";

const GRID_SIZE = 15;
const INITIAL_TURTLES = 5;
const INITIAL_BATTERIES = 3;

const createEmptyGrid = (): Grid => {
  return Array.from({ length: GRID_SIZE }, () => Array(GRID_SIZE).fill(' '));
};

export function EmojiBotArena() {
  const [grid, setGrid] = useState<Grid>(createEmptyGrid());
  const [botPosition, setBotPosition] = useState<Position>({ row: 7, col: 7 });
  const [score, setScore] = useState(0);
  const [batteries, setBatteries] = useState(0);
  const [gameOver, setGameOver] = useState(false);
  const [showGameOverScreen, setShowGameOverScreen] = useState(false);
  const [isWin, setIsWin] = useState(false);
  // Removed aiSuggestion and isAiLoading - now using tool-based mission system
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);
  const [isChatProcessing, setIsChatProcessing] = useState(false);
  const [commandLogs, setCommandLogs] = useState<string[]>([]);
  const [actionQueue, setActionQueue] = useState<GameAction[]>([]);
  const [lastCommandDebugInfo, setLastCommandDebugInfo] = useState<object | null>(null);
  const [mission, setMission] = useState<string | null>(null);
  const [workflowLog, setWorkflowLog] = useState<Array<{
    timestamp: string;
    toolName: string;
    args: any;
    result: any;
    reasoning: string;
  }>>([]);
  const [showWorkflowLog, setShowWorkflowLog] = useState(false);
  // Removed useLLMToolCalling - now always using deterministic approach

  const { toast } = useToast();

  const isBotOnMission = mission !== null;

  const placeItem = useCallback((_item: CellValue, currentGrid: Grid): Position => {
    let row, col;
    do {
      row = Math.floor(Math.random() * GRID_SIZE);
      col = Math.floor(Math.random() * GRID_SIZE);
    } while (currentGrid[row][col] !== ' ');
    return { row, col };
  }, []);

  const initializeGame = useCallback(() => {
    let newGrid = createEmptyGrid();
    
    const initialBotPos = { row: 7, col: 7 };
    newGrid[initialBotPos.row][initialBotPos.col] = '🤖';
    setBotPosition(initialBotPos);

    for (let i = 0; i < INITIAL_TURTLES; i++) {
      const pos = placeItem('🐢', newGrid);
      newGrid[pos.row][pos.col] = '🐢';
    }
    
    for (let i = 0; i < INITIAL_BATTERIES; i++) {
      const pos = placeItem('🔋', newGrid);
      newGrid[pos.row][pos.col] = '🔋';
    }

    setGrid(newGrid);
    setScore(0);
    setBatteries(3);
    setGameOver(false);
    setShowGameOverScreen(false);
    setIsWin(false);
    // Removed aiSuggestion cleanup
    setCommandLogs([]);
    setActionQueue([]);
    setLastCommandDebugInfo(null);
    setMission(null);
    setWorkflowLog([]); // Clear workflow log on reset
    setChatHistory([{ sender: 'system', message: "Use the chat to control the bot. Try 'move up' or 'collect all batteries'." }]);
  }, [placeItem]);


  const moveBot = useCallback((direction: Direction) => {
    if (gameOver) return;

    setGrid(currentGrid => {
        const newPos = { ...botPosition };
        if (direction === 'up') newPos.row--;
        if (direction === 'down') newPos.row++;
        if (direction === 'left') newPos.col--;
        if (direction === 'right') newPos.col++;

        if (
        newPos.row < 0 ||
        newPos.row >= GRID_SIZE ||
        newPos.col < 0 ||
        newPos.col >= GRID_SIZE
        ) {
            return currentGrid;
        }

        const newGrid = currentGrid.map((r) => [...r]) as Grid;
        const destinationCell = newGrid[newPos.row][newPos.col];

        if (destinationCell === '🔋') {
            setBatteries((b) => b + 1);
            setScore((s) => s + 5);
        }

        if (destinationCell === '🐢') {
            newGrid[newPos.row][newPos.col] = '💥';
            newGrid[botPosition.row][botPosition.col] = ' ';
            setBotPosition(newPos);
            setGameOver(true);
            return newGrid;
        }

        newGrid[botPosition.row][botPosition.col] = ' ';
        newGrid[newPos.row][newPos.col] = '🤖';
        
        setBotPosition(newPos);
        return newGrid;
    });
  }, [gameOver, botPosition]);

  // Check for win/lose conditions
  useEffect(() => {
    if (gameOver) {
        setMission(null);
        setIsWin(false); // Lost by hitting turtle
        setShowGameOverScreen(true);
        toast({
            title: 'Game Over!',
            description: 'You ran into a turtle!',
            variant: 'destructive',
        });
    }
  }, [gameOver, toast]);

  // Check for win condition (all turtles eliminated or high score)
  useEffect(() => {
    if (!gameOver && !showGameOverScreen) {
      const turtleCount = grid.flat().filter(cell => cell === '🐢').length;
      const batteryCount = grid.flat().filter(cell => cell === '🔋').length;

      // Win conditions:
      // 1. All turtles eliminated and some score earned
      // 2. High score achieved (100+ points)
      // 3. All batteries collected and no turtles left
      const allTurtlesGone = turtleCount === 0 && score > 0;
      const highScore = score >= 100;
      const allBatteriesCollected = batteryCount === 0 && turtleCount === 0 && score > 0;

      if (allTurtlesGone || highScore || allBatteriesCollected) {
        setIsWin(true);
        setShowGameOverScreen(true);
        setMission(null);

        let description = 'You win!';
        if (allTurtlesGone) description = 'All turtles eliminated! You win!';
        else if (highScore) description = `Amazing! You scored ${score} points!`;
        else if (allBatteriesCollected) description = 'Perfect! All batteries collected!';

        toast({
          title: 'Victory!',
          description,
          variant: 'default',
        });
      }
    }
  }, [grid, gameOver, score, showGameOverScreen, toast]);

  const fireLaser = useCallback((direction: Direction) => {
    if (gameOver || batteries <= 0) {
      if (batteries <= 0) {
        toast({ title: "No Batteries!", description: "Collect batteries to fire the laser.", variant: "destructive" });
      }
      return;
    }

    setBatteries(b => b - 1);
    const newGrid = grid.map(r => [...r]) as Grid;
    let { row, col } = botPosition;
    const path: Position[] = [];

    const dr = direction === 'up' ? -1 : direction === 'down' ? 1 : 0;
    const dc = direction === 'left' ? -1 : direction === 'right' ? 1 : 0;
    
    let turtlesHit = 0;
    
    for (let i = 1; i < GRID_SIZE; i++) {
      const nextRow = row + i * dr;
      const nextCol = col + i * dc;

      if (nextRow < 0 || nextRow >= GRID_SIZE || nextCol < 0 || nextCol >= GRID_SIZE) break;

      path.push({ row: nextRow, col: nextCol });
      const cell = newGrid[nextRow][nextCol];

      if (cell === '🐢') {
        newGrid[nextRow][nextCol] = '💥';
        turtlesHit++;
      } else if (cell === '🤖') {
      } else if (cell !== ' ' && cell !== '🔋') {
        break;
      }
    }
    
    setScore(s => s + turtlesHit * 10);
    setGrid(newGrid);

    path.forEach((p, i) => {
        setTimeout(() => {
            setGrid(g => {
                const tempGrid = g.map(r => [...r]) as Grid;
                if (tempGrid[p.row][p.col] !== '💥') {
                  tempGrid[p.row][p.col] = '✨';
                }
                return tempGrid;
            });
        }, i * 25);
    });

    setTimeout(() => {
        setGrid(g => {
            const finalGrid = g.map(r => [...r]) as Grid;
            path.forEach(p => {
                if (finalGrid[p.row][p.col] === '✨') {
                    finalGrid[p.row][p.col] = ' ';
                }
            });
            for(let r=0; r < GRID_SIZE; r++) {
                for(let c=0; c < GRID_SIZE; c++) {
                    if (finalGrid[r][c] === '💥') {
                        finalGrid[r][c] = ' ';
                    }
                }
            }
            return finalGrid;
        });
    }, path.length * 25 + 200);
  }, [gameOver, batteries, grid, botPosition, toast]);
  
  useEffect(() => {
    initializeGame();
  }, [initializeGame]);

  useEffect(() => {
    if (gameOver || actionQueue.length === 0 || isChatProcessing) {
        return;
    }

    const [currentAction, ...remainingActions] = actionQueue;

    const timeoutId = setTimeout(() => {
        if (currentAction.action === 'move' && currentAction.direction !== 'none') {
            moveBot(currentAction.direction);
        } else if (currentAction.action === 'fire' && currentAction.direction !== 'none') {
            fireLaser(currentAction.direction);
        }
        setActionQueue(remainingActions);
    }, 300); // Delay before each action

    return () => clearTimeout(timeoutId);

  }, [actionQueue, gameOver, isChatProcessing, moveBot, fireLaser]);

  // Removed getAiSuggestion function - now using tool-based mission system

  const handleChatSubmit = async (message: string) => {
    setIsChatProcessing(true);
    setChatHistory(h => [...h, { sender: 'user', message }]);
    setCommandLogs([]);
    setActionQueue([]); // Clear previous queue
    setLastCommandDebugInfo(null);
    setMission(null); // Stop any current mission when a new command is issued
    setWorkflowLog([]); // Clear workflow log for new command
    
    try {
        const turtleLocations: Position[] = [];
        const batteryLocations: Position[] = [];
        grid.forEach((row, r) => {
            row.forEach((cell, c) => {
                if (cell === '🐢') {
                    turtleLocations.push({ row: r, col: c });
                } else if (cell === '🔋') {
                    batteryLocations.push({ row: r, col: c });
                }
            });
        });

        const commandInput = { 
            command: message,
            grid,
            botRow: botPosition.row,
            botCol: botPosition.col,
            turtleLocations,
            batteryLocations,
        };
        const result = await processChatCommand(commandInput);
        console.log('🎮 Game received result:', result);
        setLastCommandDebugInfo({ input: commandInput, output: result });
        setChatHistory(h => [...h, { sender: 'bot', message: result.response }]);

        let logMessage = '';
        if (result.toolLog) {
            logMessage += `${result.toolLog}. `;
        }
        if (result.reasoning) {
            logMessage += result.reasoning;
        }

        if (logMessage) {
            setCommandLogs([logMessage.trim()]);
        }
        
        if (result.mission && result.mission !== 'none') {
            const missionName = result.mission === 'collect_batteries'
                ? 'collect all batteries'
                : 'eliminate all turtles';
            console.log('🎯 Setting mission:', missionName);
            setMission(missionName);
        } else if (result.actions && result.actions.length > 0) {
            console.log('🎮 Setting action queue:', result.actions);
            setActionQueue(result.actions.filter(a => a.action !== 'unknown'));
        }

    } catch (error) {
        console.error("Error processing chat command:", error);
        const errorMessage = "Sorry, I had trouble understanding that. Please try again.";
        setChatHistory(h => [...h, { sender: 'bot', message: errorMessage }]);
        toast({
            title: "Chat Error",
            description: "Could not process your command.",
            variant: "destructive"
        });
    } finally {
        setIsChatProcessing(false);
    }
  };

  useEffect(() => {
    if (!isBotOnMission || actionQueue.length > 0 || gameOver || isChatProcessing) {
      return;
    }

    const getNextMove = async () => {
      setIsChatProcessing(true);
      try {
        const turtleLocations: Position[] = [];
        const batteryLocations: Position[] = [];
        grid.forEach((row, r) => {
          row.forEach((cell, c) => {
            if (cell === '🐢') turtleLocations.push({ row: r, col: c });
            else if (cell === '🔋') batteryLocations.push({ row: r, col: c });
          });
        });

        if (mission?.includes('batteries') && batteryLocations.length === 0) {
          setMission(null);
          setChatHistory(h => [...h, { sender: 'bot', message: "Mission complete! All batteries collected." }]);
          setIsChatProcessing(false);
          return;
        }
        if (mission?.includes('turtles') && turtleLocations.length === 0) {
          console.log('🎉 MISSION COMPLETE: All turtles eliminated!');
          setMission(null);
          setChatHistory(h => [...h, { sender: 'bot', message: "Mission complete! All turtles eliminated." }]);
          setIsChatProcessing(false);
          return;
        }

        // Log current mission status
        if (mission?.includes('turtles')) {
          console.log(`🎯 MISSION STATUS: ${turtleLocations.length} turtles remaining at:`,
            turtleLocations.map(t => `(${t.row},${t.col})`).join(', '));
        }
        
        const input = {
          grid,
          botRow: botPosition.row,
          botCol: botPosition.col,
          objective: mission!,
          turtleLocations,
          batteryLocations,
          currentBatteries: batteries, // Pass current battery count
        };

        // Use deterministic tool calling approach
        console.log('🔧 Using deterministic tool calling approach');
        const missionResult = await executeToolBasedMission(input);
        console.log('🔧 Tool calling result:', missionResult);

        // Update workflow log with new steps
        if (missionResult.workflowSteps && missionResult.workflowSteps.length > 0) {
          setWorkflowLog(prev => [...prev, ...missionResult.workflowSteps]);
          console.log('🔧 Workflow steps:', missionResult.workflowSteps);
        }

        const suggestion = {
          direction: missionResult.direction,
          reason: `${missionResult.reason} | ${missionResult.finalAnalysis}`
        };
        setLastCommandDebugInfo({ input, output: missionResult });

        if (suggestion.direction === 'none') {
          // Check if this is a "ready to fire" situation for turtle missions
          // Mission ended with no direction
          setMission(null);
          setChatHistory(h => [...h, { sender: 'bot', message: `Mission ended. ${suggestion.reason}` }]);
        } else {
          // Use the action directly from the AI mission result
          if (missionResult.action === 'fire') {
            console.log(`🔥 EXECUTING FIRE ACTION: ${suggestion.direction} at target`);
            console.log(`🎯 Before fire: ${turtleLocations.length} turtles remaining`);
            fireLaser(suggestion.direction);
            console.log(`🔄 Fire action executed, mission will continue automatically`);
          } else {
            setActionQueue([{ action: 'move', direction: suggestion.direction }]);
            console.log(`🚶 EXECUTING MOVE ACTION: ${suggestion.direction} toward target`);
          }
        }
      } catch (error) {
        console.error("Error during mission step:", error);
        toast({ title: "Mission AI Error", description: "The bot got confused and the mission was aborted.", variant: "destructive" });
        setMission(null);
      } finally {
        setIsChatProcessing(false);
      }
    };

    const timeoutId = setTimeout(getNextMove, 800); // Longer delay for better processing

    return () => clearTimeout(timeoutId);
  }, [isBotOnMission, mission, grid, botPosition, actionQueue, gameOver, isChatProcessing, toast, fireLaser]);

  // Removed test functions for game over screens


  return (
    <>
      <div className="grid md:grid-cols-[1fr_400px] gap-6 p-4 md:p-6">
        <div className="flex flex-col items-center justify-center">
          <GameGrid grid={grid} />
        </div>
        <GameStats
          score={score}
          batteries={batteries}
          onMove={moveBot}
          onFire={fireLaser}
          onReset={initializeGame}
          gameOver={gameOver}
          chatHistory={chatHistory}
          onChatSubmit={handleChatSubmit}
          isChatProcessing={isChatProcessing || isBotOnMission}
          commandLogs={commandLogs}
          isBotOnMission={isBotOnMission}
          mission={mission}
          lastCommandDebugInfo={lastCommandDebugInfo}
          workflowLog={workflowLog}
          showWorkflowLog={showWorkflowLog}
          onToggleWorkflowLog={() => setShowWorkflowLog(!showWorkflowLog)}
        />
      </div>

      {/* Game Over Screen */}
      {showGameOverScreen && (
        <GameOverScreen
          key={`game-over-${isWin ? 'win' : 'lose'}-${Date.now()}`}
          isWin={isWin}
          score={score}
          onRestart={() => {
            console.log('🔄 Restart button clicked');
            setShowGameOverScreen(false);
            initializeGame();
          }}
          onClose={() => {
            console.log('🔴 Parent onClose called, hiding game over screen');
            console.log('🔴 Current showGameOverScreen state:', showGameOverScreen);
            setShowGameOverScreen(false);
            console.log('🔴 setShowGameOverScreen(false) called');
          }}
        />
      )}
    </>
  );
}
