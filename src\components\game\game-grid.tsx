import type { Grid } from '@/lib/types';
import { cn } from '@/lib/utils';

interface GameGridProps {
  grid: Grid;
}

export function GameGrid({ grid }: GameGridProps) {
  return (
    <div
      className="grid border-2 border-primary rounded-lg shadow-lg bg-card"
      style={{
        gridTemplateColumns: `repeat(${grid.length}, 1fr)`,
        width: '600px',
        height: '600px',
      }}
    >
      {grid.map((row, rowIndex) =>
        row.map((cell, colIndex) => (
          <div
            key={`${rowIndex}-${colIndex}`}
            className="flex items-center justify-center border border-border/50 text-2xl md:text-3xl transition-all duration-200"
          >
            <span className={cn(
              'transform transition-transform duration-300 ease-out',
              cell === '🤖' && 'animate-pulse',
              cell === '💥' && 'animate-ping'
            )}>
              {cell}
            </span>
          </div>
        ))
      )}
    </div>
  );
}
