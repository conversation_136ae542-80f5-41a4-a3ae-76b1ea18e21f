import type { Grid } from '@/lib/types';
import { cn } from '@/lib/utils';

interface GameGridProps {
  grid: Grid;
}

export function GameGrid({ grid }: GameGridProps) {
  return (
    <div className="relative">
      {/* Cyberpunk grid background with animated scan lines */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900/95 to-slate-800/95 rounded-lg"></div>

      {/* Animated scan lines */}
      <div className="absolute inset-0 overflow-hidden rounded-lg">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-cyan-500/5 to-transparent animate-pulse"></div>
        <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-cyan-400/60 to-transparent animate-ping"></div>
      </div>

      {/* Main game grid */}
      <div
        className="relative grid border-2 border-cyan-400/50 rounded-lg shadow-[0_0_30px_rgba(34,211,238,0.3)] bg-slate-900/50 backdrop-blur-sm"
        style={{
          gridTemplateColumns: `repeat(${grid.length}, 1fr)`,
          width: '600px',
          height: '600px',
        }}
      >
        {grid.map((row, rowIndex) =>
          row.map((cell, colIndex) => (
            <div
              key={`${rowIndex}-${colIndex}`}
              className={cn(
                "flex items-center justify-center border text-2xl md:text-3xl transition-all duration-300 relative group",
                "hover:scale-105 hover:z-10",
                // Cell-specific styling
                cell === '🤖' && "border-cyan-400/60 bg-gradient-to-br from-cyan-500/20 to-blue-500/20 shadow-[0_0_15px_rgba(34,211,238,0.4)]",
                cell === '🐢' && "border-red-400/60 bg-gradient-to-br from-red-500/20 to-orange-500/20 shadow-[0_0_15px_rgba(239,68,68,0.4)]",
                cell === '🔋' && "border-yellow-400/60 bg-gradient-to-br from-yellow-500/20 to-amber-500/20 shadow-[0_0_15px_rgba(234,179,8,0.4)]",
                cell === '💥' && "border-orange-400/60 bg-gradient-to-br from-orange-500/30 to-red-500/30 shadow-[0_0_20px_rgba(249,115,22,0.6)]",
                cell === ' ' && "border-slate-600/30 bg-slate-800/20 hover:border-cyan-500/40 hover:bg-cyan-500/5"
              )}
            >
              <span className={cn(
                'transform transition-all duration-300 ease-out drop-shadow-lg filter brightness-110 relative z-10',
                cell === '🤖' && 'animate-pulse text-cyan-300',
                cell === '💥' && 'animate-ping text-orange-300',
                cell === '🐢' && 'hover:animate-bounce text-red-300',
                cell === '🔋' && 'hover:animate-pulse text-yellow-300'
              )}>
                {cell}
              </span>

              {/* Hover glow effect */}
              {cell !== ' ' && (
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded"></div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Corner accent lights */}
      <div className="absolute -top-1 -left-1 w-2 h-2 bg-cyan-400 rounded-full blur-sm opacity-80 animate-pulse"></div>
      <div className="absolute -top-1 -right-1 w-2 h-2 bg-purple-400 rounded-full blur-sm opacity-80 animate-pulse"></div>
      <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-pink-400 rounded-full blur-sm opacity-80 animate-pulse"></div>
      <div className="absolute -bottom-1 -right-1 w-2 h-2 bg-cyan-400 rounded-full blur-sm opacity-80 animate-pulse"></div>

      {/* Data stream effect */}
      <div className="absolute top-2 right-2 text-xs text-cyan-400/60 font-mono">
        GRID_SYS_v2.1
      </div>
    </div>
  );
}
