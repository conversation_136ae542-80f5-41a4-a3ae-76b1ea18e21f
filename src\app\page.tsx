import { EmojiBotArena } from '@/components/game/emoji-bot-arena';
import { Logo } from '@/components/icons/logo';

export default function Home() {
  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Cyberpunk animated header */}
      <header className="relative flex h-20 items-center gap-4 px-4 md:px-6 overflow-hidden">
        {/* Animated background */}
        <div className="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-purple-900/80 to-slate-900/90 backdrop-blur-sm"></div>

        {/* Animated scan lines */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-cyan-400/80 to-transparent animate-pulse"></div>
          <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-purple-400/80 to-transparent animate-pulse"></div>

          {/* Moving data streams */}
          <div className="absolute top-2 left-0 w-2 h-2 bg-cyan-400/60 rounded-full animate-ping"></div>
          <div className="absolute top-4 right-10 w-1 h-1 bg-purple-400/60 rounded-full animate-pulse"></div>
          <div className="absolute bottom-2 left-20 w-1.5 h-1.5 bg-pink-400/60 rounded-full animate-bounce"></div>

          {/* Removed grid pattern for cleaner look */}
        </div>

        <nav className="relative z-10 flex w-full items-center gap-4">
          <a href="#" className="flex items-center gap-3 group">
            <div className="relative">
              <Logo />
              {/* Glow effect around logo */}
              <div className="absolute inset-0 bg-cyan-400/20 rounded-full blur-lg group-hover:bg-cyan-400/40 transition-all duration-300"></div>
            </div>
            <div className="flex flex-col">
              <span className="text-xl font-mono font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 animate-pulse">
                ◢ DevX STUDIO ◣
              </span>
              <span className="text-xs font-mono text-cyan-300/70 tracking-wider">
                AGENT_DEMO_v2.1
              </span>
            </div>
          </a>

          {/* Status indicators */}
          <div className="ml-auto flex items-center gap-4">
            <div className="flex items-center gap-2 text-xs font-mono text-green-400">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              SYSTEM_ONLINE
            </div>
            <div className="flex items-center gap-2 text-xs font-mono text-cyan-400">
              <div className="w-2 h-2 bg-cyan-400 rounded-full animate-ping"></div>
              AI_ACTIVE
            </div>
          </div>
        </nav>

        {/* Corner accent lights */}
        <div className="absolute top-0 left-0 w-3 h-3 bg-cyan-400/60 rounded-full blur-sm animate-pulse"></div>
        <div className="absolute top-0 right-0 w-3 h-3 bg-purple-400/60 rounded-full blur-sm animate-pulse"></div>
        <div className="absolute bottom-0 left-0 w-3 h-3 bg-pink-400/60 rounded-full blur-sm animate-pulse"></div>
        <div className="absolute bottom-0 right-0 w-3 h-3 bg-cyan-400/60 rounded-full blur-sm animate-pulse"></div>
      </header>

      <main className="flex-1 overflow-auto relative">
        <EmojiBotArena />
      </main>
    </div>
  );
}
