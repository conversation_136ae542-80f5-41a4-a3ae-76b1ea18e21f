'use server';

/**
 * @fileOverview Completely deterministic mission planning - no LLM calls for calculations
 */

export type DeterministicMissionInput = {
  grid: string[][];
  botRow: number;
  botCol: number;
  objective: string;
  turtleLocations: Array<{row: number, col: number}>;
  batteryLocations: Array<{row: number, col: number}>;
};

export type DeterministicMissionOutput = {
  action: 'move' | 'fire';
  direction: 'up' | 'down' | 'left' | 'right' | 'none';
  reason: string;
  targetAnalysis: string;
  distanceCalculations: string;
};

// Helper functions for calculations
function calculateDistance(from: {row: number, col: number}, to: {row: number, col: number}): number {
  return Math.abs(to.row - from.row) + Math.abs(to.col - from.col);
}

function findBestTarget(
  botRow: number, 
  botCol: number, 
  targets: Array<{row: number, col: number}>, 
  obstacles: Array<{row: number, col: number}> = []
) {
  const analyzed = targets.map(target => {
    const distance = calculateDistance({row: botRow, col: botCol}, target);
    
    let score = 20 - distance; // Base score from distance
    
    // Bonus for same row/column (easier path)
    if (target.row === botRow) score += 10; // Same row
    if (target.col === botCol) score += 15; // Same column (even better)
    
    // Penalty for nearby obstacles
    const nearbyObstacles = obstacles.filter(obs => 
      Math.abs(obs.row - target.row) <= 1 && Math.abs(obs.col - target.col) <= 1
    ).length;
    score -= nearbyObstacles * 3;
    
    return { target, distance, score };
  });
  
  analyzed.sort((a, b) => b.score - a.score);
  return analyzed[0];
}

function getDirectionToTarget(botRow: number, botCol: number, targetRow: number, targetCol: number): 'up' | 'down' | 'left' | 'right' | 'none' {
  if (targetRow > botRow) return 'down';
  if (targetRow < botRow) return 'up';
  if (targetCol > botCol) return 'right';
  if (targetCol < botCol) return 'left';
  return 'none';
}

function canFireAtTarget(botRow: number, botCol: number, targetRow: number, targetCol: number): {canFire: boolean, direction?: 'up' | 'down' | 'left' | 'right'} {
  if (botRow === targetRow) {
    return { canFire: true, direction: targetCol > botCol ? 'right' : 'left' };
  }
  if (botCol === targetCol) {
    return { canFire: true, direction: targetRow > botRow ? 'down' : 'up' };
  }
  return { canFire: false };
}

export function executeDeterministicMission(input: DeterministicMissionInput): DeterministicMissionOutput {
  const { botRow, botCol, objective, batteryLocations, turtleLocations } = input;
  
  console.log('🔧 Executing deterministic mission logic');
  console.log(`Bot at (${botRow}, ${botCol}), Objective: ${objective}`);
  
  if (objective.includes('batteries') && batteryLocations.length > 0) {
    // Battery collection mission
    const distances = batteryLocations.map(battery => ({
      battery,
      distance: calculateDistance({row: botRow, col: botCol}, battery)
    }));
    
    const distanceCalculations = distances.map(d => 
      `Battery at (${d.battery.row},${d.battery.col}): distance ${d.distance}`
    ).join(', ');
    
    const best = findBestTarget(botRow, botCol, batteryLocations, turtleLocations);
    const direction = getDirectionToTarget(botRow, botCol, best.target.row, best.target.col);
    
    const targetAnalysis = `Selected battery at (${best.target.row},${best.target.col}) with score ${best.score} (distance: ${best.distance})`;
    const reason = `Moving ${direction} toward optimal battery target. Target chosen based on distance and path safety.`;
    
    console.log(`✅ Battery mission: ${direction} toward (${best.target.row},${best.target.col})`);
    
    return {
      action: 'move',
      direction,
      reason,
      targetAnalysis,
      distanceCalculations
    };
    
  } else if (objective.includes('turtle') && turtleLocations.length > 0) {
    // Turtle elimination mission
    const distances = turtleLocations.map(turtle => ({
      turtle,
      distance: calculateDistance({row: botRow, col: botCol}, turtle)
    }));
    
    const distanceCalculations = distances.map(d => 
      `Turtle at (${d.turtle.row},${d.turtle.col}): distance ${d.distance}`
    ).join(', ');
    
    // Check if we can fire at any turtle
    for (const turtle of turtleLocations) {
      const fireCheck = canFireAtTarget(botRow, botCol, turtle.row, turtle.col);
      if (fireCheck.canFire) {
        const targetAnalysis = `Can fire at turtle at (${turtle.row},${turtle.col}) from current position`;
        const reason = `Firing ${fireCheck.direction} at turtle at (${turtle.row},${turtle.col}) - clear line of sight`;
        
        console.log(`✅ Turtle mission: Fire ${fireCheck.direction} at (${turtle.row},${turtle.col})`);
        
        return {
          action: 'fire',
          direction: fireCheck.direction!,
          reason,
          targetAnalysis,
          distanceCalculations
        };
      }
    }
    
    // Need to move to get line of sight
    const best = findBestTarget(botRow, botCol, turtleLocations, []);
    const direction = getDirectionToTarget(botRow, botCol, best.target.row, best.target.col);
    
    const targetAnalysis = `Selected turtle at (${best.target.row},${best.target.col}) for positioning`;
    const reason = `Moving ${direction} to align with turtle at (${best.target.row},${best.target.col}) for clear shot`;
    
    console.log(`✅ Turtle mission: Move ${direction} toward (${best.target.row},${best.target.col})`);
    
    return {
      action: 'move',
      direction,
      reason,
      targetAnalysis,
      distanceCalculations
    };
    
  } else {
    // No targets available
    console.log('✅ Mission complete - no targets');
    
    return {
      action: 'move',
      direction: 'none',
      reason: 'Mission complete - no more targets available',
      targetAnalysis: 'No valid targets found',
      distanceCalculations: 'No calculations needed'
    };
  }
}
