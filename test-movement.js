// Test script to verify movement logic
// Run with: node test-movement.js

require('dotenv').config();

const { createStructuredCompletion } = require('./src/ai/openai-client.ts');
const { z } = require('zod');

const SuggestMoveDirectionOutputSchema = z.object({
  direction: z.enum(['up', 'down', 'left', 'right', 'none']),
  reason: z.string(),
});

async function testMovementLogic() {
  console.log('Testing movement logic...');

  // Test case: Bot at (6,1), Battery at (9,1) - should move DOWN
  const testInput = {
    botRow: 6,
    botCol: 1,
    objective: "collect all batteries",
    batteryLocations: [{ row: 9, col: 1 }],
    turtleLocations: [
      { row: 12, col: 4 },
      { row: 13, col: 1 }
    ]
  };

  const gridDisplay = Array(15).fill().map((_, r) => 
    Array(15).fill().map((_, c) => {
      if (r === 6 && c === 1) return '🤖';
      if (r === 9 && c === 1) return '🔋';
      if (r === 12 && c === 4) return '🐢';
      if (r === 13 && c === 1) return '🐢';
      return ' ';
    }).join('')
  ).join('\n');

  const batteryInfo = testInput.batteryLocations.map(loc => `  - row ${loc.row}, col ${loc.col}`).join('\n');
  const turtleInfo = testInput.turtleLocations.map(loc => `  - row ${loc.row}, col ${loc.col}`).join('\n');

  const prompt = `You are a meticulous and logical AI assistant playing the Emoji Bot Arena game. Your task is to suggest the single most optimal move for the bot to achieve a given objective. You must be precise and avoid getting stuck in loops.

**CRITICAL COORDINATE SYSTEM UNDERSTANDING:**
- Row 0 is at the TOP of the grid
- Row numbers INCREASE as you go DOWN
- Column 0 is at the LEFT of the grid  
- Column numbers INCREASE as you go RIGHT
- Moving 'up' DECREASES row number (towards row 0)
- Moving 'down' INCREASES row number (away from row 0)
- Moving 'left' DECREASES column number (towards column 0)
- Moving 'right' INCREASES column number (away from column 0)

**1. Analyze the Game State:**
- The bot 🤖 is at row ${testInput.botRow}, column ${testInput.botCol}.
- The user's high-level objective is: "${testInput.objective}"
- The grid is:
${gridDisplay}
- Batteries 🔋 locations:
${batteryInfo}
- Turtles 🐢 locations:
${turtleInfo}

**2. Determine the Best Move based on Objective:**

**If the objective contains "batteries":**
a. Find the battery with the minimum Manhattan distance from the bot. This is your target for this turn. Tie-break using lower row, then lower column.
b. **Choose Direction Logic - FOLLOW EXACTLY:**
   - If target row > bot row: move 'down' (increase row number)
   - If target row < bot row: move 'up' (decrease row number)  
   - If target row = bot row AND target col > bot col: move 'right' (increase column)
   - If target row = bot row AND target col < bot col: move 'left' (decrease column)
   - If target row = bot row AND target col = bot col: target reached, return 'none'
c. **Safety Check**:
   - Check if your chosen move lands on a turtle ('🐢'). If so, try alternative moves.
   - If no safe direct move exists, try moving perpendicular to go around obstacles.
   - If no safe move is possible, return 'none'.
d. Provide a reason stating the target location and your chosen move with clear coordinate logic.

**EXAMPLE COORDINATE LOGIC:**
- Bot at (6,1), Battery at (9,1): target row 9 > bot row 6, so move 'down'
- Bot at (6,1), Battery at (3,1): target row 3 < bot row 6, so move 'up'  
- Bot at (6,1), Battery at (6,5): same row, target col 5 > bot col 1, so move 'right'

Respond with a JSON object in this exact format:
{
  "direction": "<direction>",
  "reason": "<reason>"
}

Make sure the direction is one of: up, down, left, right, or none.`;

  try {
    const result = await createStructuredCompletion(prompt, SuggestMoveDirectionOutputSchema);
    
    console.log('✅ Test Result:');
    console.log('Direction:', result.direction);
    console.log('Reason:', result.reason);
    
    // Expected: direction should be "down" since target row (9) > bot row (6)
    if (result.direction === 'down') {
      console.log('✅ CORRECT: Bot should move down to reach battery');
    } else {
      console.log('❌ INCORRECT: Expected "down", got "' + result.direction + '"');
      console.log('Bot is at (6,1), battery is at (9,1)');
      console.log('Since 9 > 6, bot should move DOWN to increase row number');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_BASE) {
  testMovementLogic();
} else {
  console.log('⚠️  Please set OPENAI_API_KEY and OPENAI_API_BASE in .env file to run this test');
}
