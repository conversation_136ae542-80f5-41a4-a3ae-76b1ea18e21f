'use server';

/**
 * @fileOverview Tool-based mission planning using OpenAI function calling
 * This demonstrates the tool calling approach vs structured output
 */

// Simple reliable mission execution without complex tool calling

export type ToolBasedMissionInput = {
  grid: string[][];
  botRow: number;
  botCol: number;
  objective: string;
  turtleLocations: Array<{row: number, col: number}>;
  batteryLocations: Array<{row: number, col: number}>;
};

export type ToolBasedMissionOutput = {
  action: 'move' | 'fire';
  direction: 'up' | 'down' | 'left' | 'right' | 'none';
  reason: string;
  toolCallsLog: string;
  finalAnalysis: string;
};

// Tool functions that the LLM can call
function calculateDistances(args: {
  botRow: number;
  botCol: number;
  targets: Array<{row: number, col: number}>;
}): Array<{target: {row: number, col: number}, distance: number}> {
  const { botRow, botCol, targets } = args;
  
  return targets.map(target => ({
    target,
    distance: Math.abs(target.row - botRow) + Math.abs(target.col - botCol)
  })).sort((a, b) => a.distance - b.distance);
}

function findBestTarget(args: {
  botRow: number;
  botCol: number;
  targets: Array<{row: number, col: number}>;
  obstacles: Array<{row: number, col: number}>;
}): {target: {row: number, col: number}, score: number, reason: string} {
  const { botRow, botCol, targets, obstacles } = args;
  
  let bestTarget = targets[0];
  let bestScore = -1;
  let reason = '';
  
  for (const target of targets) {
    const distance = Math.abs(target.row - botRow) + Math.abs(target.col - botCol);
    
    // Prefer targets in same row/column (easier path)
    const sameRowCol = (target.row === botRow || target.col === botCol) ? 10 : 0;
    
    // Check for obstacles in path
    const hasObstacles = obstacles.some(obs => 
      (obs.row === botRow && obs.col === target.col) ||
      (obs.col === botCol && obs.row === target.row)
    );
    const obstaclesPenalty = hasObstacles ? -5 : 0;
    
    const score = (100 / distance) + sameRowCol + obstaclesPenalty;
    
    if (score > bestScore) {
      bestScore = score;
      bestTarget = target;
      reason = `Target at (${target.row},${target.col}) chosen: distance=${distance}, same row/col bonus=${sameRowCol}, obstacles penalty=${obstaclesPenalty}`;
    }
  }
  
  return { target: bestTarget, score: bestScore, reason };
}

function getNextMove(args: {
  botRow: number;
  botCol: number;
  targetRow: number;
  targetCol: number;
}): {direction: 'up' | 'down' | 'left' | 'right', reason: string} {
  const { botRow, botCol, targetRow, targetCol } = args;
  
  const rowDiff = targetRow - botRow;
  const colDiff = targetCol - botCol;
  
  if (Math.abs(rowDiff) > Math.abs(colDiff)) {
    if (rowDiff > 0) {
      return { direction: 'down', reason: `Moving down to reach target row ${targetRow}` };
    } else {
      return { direction: 'up', reason: `Moving up to reach target row ${targetRow}` };
    }
  } else {
    if (colDiff > 0) {
      return { direction: 'right', reason: `Moving right to reach target column ${targetCol}` };
    } else {
      return { direction: 'left', reason: `Moving left to reach target column ${targetCol}` };
    }
  }
}

function canFireAtTarget(args: {
  botRow: number;
  botCol: number;
  targetRow: number;
  targetCol: number;
}): {canFire: boolean, direction?: 'up' | 'down' | 'left' | 'right', reason: string} {
  const { botRow, botCol, targetRow, targetCol } = args;

  if (botRow === targetRow) {
    const direction = targetCol > botCol ? 'right' : 'left';
    return {
      canFire: true,
      direction,
      reason: `Can fire ${direction} at target in same row`
    };
  }

  if (botCol === targetCol) {
    const direction = targetRow > botRow ? 'down' : 'up';
    return {
      canFire: true,
      direction,
      reason: `Can fire ${direction} at target in same column`
    };
  }

  return {
    canFire: false,
    reason: 'Target not in line of sight - need to move first'
  };
}



export async function executeToolBasedMission(input: ToolBasedMissionInput): Promise<ToolBasedMissionOutput> {
  console.log('🔧 Executing tool-based mission logic');
  console.log(`Bot at (${input.botRow}, ${input.botCol}), Objective: ${input.objective}`);

  // SIMPLE RELIABLE APPROACH - No complex tool calling for now
  // Just use direct calculation to avoid game resets

  if (input.objective.includes('batteries') && input.batteryLocations.length > 0) {
    // Find closest battery
    const distances = input.batteryLocations.map(battery => ({
      battery,
      distance: Math.abs(battery.row - input.botRow) + Math.abs(battery.col - input.botCol)
    }));

    distances.sort((a, b) => a.distance - b.distance);
    const closestBattery = distances[0].battery;

    // Calculate direction to move
    const rowDiff = closestBattery.row - input.botRow;
    const colDiff = closestBattery.col - input.botCol;

    let direction: 'up' | 'down' | 'left' | 'right';
    if (Math.abs(rowDiff) > Math.abs(colDiff)) {
      direction = rowDiff > 0 ? 'down' : 'up';
    } else {
      direction = colDiff > 0 ? 'right' : 'left';
    }

    return {
      action: 'move',
      direction,
      reason: `Moving ${direction} toward closest battery at (${closestBattery.row}, ${closestBattery.col})`,
      toolCallsLog: `Direct calculation: closest battery distance ${distances[0].distance}`,
      finalAnalysis: 'Using simple reliable calculation'
    };
  }

  if (input.objective.includes('turtles') && input.turtleLocations.length > 0) {
    // Find closest turtle
    const distances = input.turtleLocations.map(turtle => ({
      turtle,
      distance: Math.abs(turtle.row - input.botRow) + Math.abs(turtle.col - input.botCol)
    }));

    distances.sort((a, b) => a.distance - b.distance);
    const closestTurtle = distances[0].turtle;

    // Check if we can fire at it
    const sameRow = closestTurtle.row === input.botRow;
    const sameCol = closestTurtle.col === input.botCol;

    if (sameRow || sameCol) {
      let direction: 'up' | 'down' | 'left' | 'right';
      if (sameRow) {
        direction = closestTurtle.col > input.botCol ? 'right' : 'left';
      } else {
        direction = closestTurtle.row > input.botRow ? 'down' : 'up';
      }

      return {
        action: 'fire',
        direction,
        reason: `Firing ${direction} at turtle at (${closestTurtle.row}, ${closestTurtle.col})`,
        toolCallsLog: `Direct calculation: turtle in line of sight`,
        finalAnalysis: 'Using simple reliable calculation'
      };
    } else {
      // Move to align with turtle
      const rowDiff = closestTurtle.row - input.botRow;
      const colDiff = closestTurtle.col - input.botCol;

      let direction: 'up' | 'down' | 'left' | 'right';
      if (Math.abs(rowDiff) > Math.abs(colDiff)) {
        direction = rowDiff > 0 ? 'down' : 'up';
      } else {
        direction = colDiff > 0 ? 'right' : 'left';
      }

      return {
        action: 'move',
        direction,
        reason: `Moving ${direction} to align with turtle at (${closestTurtle.row}, ${closestTurtle.col})`,
        toolCallsLog: `Direct calculation: moving to align`,
        finalAnalysis: 'Using simple reliable calculation'
      };
    }
  }

  // Fallback
  return {
    action: 'move',
    direction: 'up',
    reason: 'No targets found, moving up',
    toolCallsLog: 'No targets available',
    finalAnalysis: 'Fallback movement'
  };

}
