'use server';

/**
 * @fileOverview Tool-based mission planning using OpenAI function calling
 * This demonstrates the tool calling approach vs structured output
 */

import { createToolCompletion, type ToolFunction } from '@/ai/openai-client';

export type ToolBasedMissionInput = {
  grid: string[][];
  botRow: number;
  botCol: number;
  objective: string;
  turtleLocations: Array<{row: number, col: number}>;
  batteryLocations: Array<{row: number, col: number}>;
};

export type ToolBasedMissionOutput = {
  action: 'move' | 'fire';
  direction: 'up' | 'down' | 'left' | 'right' | 'none';
  reason: string;
  toolCallsLog: string;
  finalAnalysis: string;
};

// Tool functions that the LLM can call
function calculateDistances(args: {
  botRow: number;
  botCol: number;
  targets: Array<{row: number, col: number}>;
}): Array<{target: {row: number, col: number}, distance: number}> {
  const { botRow, botCol, targets } = args;
  
  return targets.map(target => ({
    target,
    distance: Math.abs(target.row - botRow) + Math.abs(target.col - botCol)
  })).sort((a, b) => a.distance - b.distance);
}

function findBestTarget(args: {
  botRow: number;
  botCol: number;
  targets: Array<{row: number, col: number}>;
  obstacles: Array<{row: number, col: number}>;
}): {target: {row: number, col: number}, score: number, reason: string} {
  const { botRow, botCol, targets, obstacles } = args;
  
  let bestTarget = targets[0];
  let bestScore = -1;
  let reason = '';
  
  for (const target of targets) {
    const distance = Math.abs(target.row - botRow) + Math.abs(target.col - botCol);
    
    // Prefer targets in same row/column (easier path)
    const sameRowCol = (target.row === botRow || target.col === botCol) ? 10 : 0;
    
    // Check for obstacles in path
    const hasObstacles = obstacles.some(obs => 
      (obs.row === botRow && obs.col === target.col) ||
      (obs.col === botCol && obs.row === target.row)
    );
    const obstaclesPenalty = hasObstacles ? -5 : 0;
    
    const score = (100 / distance) + sameRowCol + obstaclesPenalty;
    
    if (score > bestScore) {
      bestScore = score;
      bestTarget = target;
      reason = `Target at (${target.row},${target.col}) chosen: distance=${distance}, same row/col bonus=${sameRowCol}, obstacles penalty=${obstaclesPenalty}`;
    }
  }
  
  return { target: bestTarget, score: bestScore, reason };
}

function getNextMove(args: {
  botRow: number;
  botCol: number;
  targetRow: number;
  targetCol: number;
}): {direction: 'up' | 'down' | 'left' | 'right', reason: string} {
  const { botRow, botCol, targetRow, targetCol } = args;
  
  const rowDiff = targetRow - botRow;
  const colDiff = targetCol - botCol;
  
  if (Math.abs(rowDiff) > Math.abs(colDiff)) {
    if (rowDiff > 0) {
      return { direction: 'down', reason: `Moving down to reach target row ${targetRow}` };
    } else {
      return { direction: 'up', reason: `Moving up to reach target row ${targetRow}` };
    }
  } else {
    if (colDiff > 0) {
      return { direction: 'right', reason: `Moving right to reach target column ${targetCol}` };
    } else {
      return { direction: 'left', reason: `Moving left to reach target column ${targetCol}` };
    }
  }
}

function canFireAtTarget(args: {
  botRow: number;
  botCol: number;
  targetRow: number;
  targetCol: number;
}): {canFire: boolean, direction?: 'up' | 'down' | 'left' | 'right', reason: string} {
  const { botRow, botCol, targetRow, targetCol } = args;

  if (botRow === targetRow) {
    const direction = targetCol > botCol ? 'right' : 'left';
    return {
      canFire: true,
      direction,
      reason: `Can fire ${direction} at target in same row`
    };
  }

  if (botCol === targetCol) {
    const direction = targetRow > botRow ? 'down' : 'up';
    return {
      canFire: true,
      direction,
      reason: `Can fire ${direction} at target in same column`
    };
  }

  return {
    canFire: false,
    reason: 'Target not in line of sight - need to move first'
  };
}



export async function executeToolBasedMission(input: ToolBasedMissionInput): Promise<ToolBasedMissionOutput> {
  console.log('🔧 Executing tool-based mission logic');
  console.log(`Bot at (${input.botRow}, ${input.botCol}), Objective: ${input.objective}`);

  // Define the tools available to the LLM
  const tools: ToolFunction[] = [
    {
      name: 'calculate_distances',
      description: 'Calculate Manhattan distances from bot to multiple targets',
      parameters: {
        type: 'object',
        properties: {
          botRow: { type: 'number', description: 'Bot row position' },
          botCol: { type: 'number', description: 'Bot column position' },
          targets: { 
            type: 'array', 
            items: { 
              type: 'object',
              properties: {
                row: { type: 'number' },
                col: { type: 'number' }
              }
            },
            description: 'Array of target positions'
          }
        },
        required: ['botRow', 'botCol', 'targets']
      },
      handler: calculateDistances
    },
    {
      name: 'find_best_target',
      description: 'Find the optimal target considering distance, path, and obstacles',
      parameters: {
        type: 'object',
        properties: {
          botRow: { type: 'number' },
          botCol: { type: 'number' },
          targets: { type: 'array', items: { type: 'object' } },
          obstacles: { type: 'array', items: { type: 'object' } }
        },
        required: ['botRow', 'botCol', 'targets', 'obstacles']
      },
      handler: findBestTarget
    },
    {
      name: 'get_next_move',
      description: 'Determine the next move direction toward a target',
      parameters: {
        type: 'object',
        properties: {
          botRow: { type: 'number' },
          botCol: { type: 'number' },
          targetRow: { type: 'number' },
          targetCol: { type: 'number' }
        },
        required: ['botRow', 'botCol', 'targetRow', 'targetCol']
      },
      handler: getNextMove
    },
    {
      name: 'can_fire_at_target',
      description: 'Check if bot can fire at a target and determine direction',
      parameters: {
        type: 'object',
        properties: {
          botRow: { type: 'number' },
          botCol: { type: 'number' },
          targetRow: { type: 'number' },
          targetCol: { type: 'number' }
        },
        required: ['botRow', 'botCol', 'targetRow', 'targetCol']
      },
      handler: canFireAtTarget
    }
  ];

  // Create the prompt for the LLM
  const gridDisplay = input.grid.map(row => row.join('')).join('\n');
  
  const messages = [
    {
      role: 'system' as const,
      content: `You are an expert game AI strategist. Use the available tools to analyze the game state and determine the best action.

Available tools:
- calculate_distances: Get distances to targets
- find_best_target: Find optimal target considering obstacles
- get_next_move: Get direction to move toward target
- can_fire_at_target: Check if you can fire at a target
- make_final_decision: Make the final action decision based on your analysis

WORKFLOW:
1. Use analysis tools to gather information (calculate_distances, find_best_target, etc.)
2. After using tools, provide your final decision in your response
3. Your final response MUST end with: "DECISION: MOVE [direction]" or "DECISION: FIRE [direction]"

Always use tools to make decisions rather than guessing.`
    },
    {
      role: 'user' as const,
      content: `Analyze this game state and determine the best action:

Objective: ${input.objective}
Bot position: (${input.botRow}, ${input.botCol})

Grid:
${gridDisplay}

Batteries: ${input.batteryLocations.map(b => `(${b.row},${b.col})`).join(', ')}
Turtles: ${input.turtleLocations.map(t => `(${t.row},${t.col})`).join(', ')}

INSTRUCTIONS:
1. Use the tools to analyze the situation (calculate distances, find best target, etc.)
2. After gathering information, provide your FINAL DECISION
3. Your final response must end with: "DECISION: MOVE [direction]" or "DECISION: FIRE [direction]"

Example final responses:
- "Based on analysis, the closest battery is 2 steps away. DECISION: MOVE up"
- "Turtle is in line of sight. DECISION: FIRE right"

Use the tools to analyze the situation and then provide your final recommendation.`
    }
  ];

  try {
    console.log('🔧 Calling createToolCompletion with', tools.length, 'tools');
    const result = await createToolCompletion(messages, tools);
    console.log('🔧 Tool completion result:', {
      toolCallsCount: result.toolCalls.length,
      finalResponse: result.finalResponse?.substring(0, 100) + '...',
      toolNames: result.toolCalls.map(tc => tc.name)
    });

    // Extract action from the final response
    const finalResponse = result.finalResponse || '';
    const toolCallsLog = result.toolCalls.map(tc =>
      `${tc.name}(${JSON.stringify(tc.arguments)}) -> ${JSON.stringify(tc.result)}`
    ).join('\n');

    // Parse the final response for the decision
    let action: 'move' | 'fire' = 'move';
    let direction: 'up' | 'down' | 'left' | 'right' | 'none' = 'none';
    let reason = finalResponse;

    // Look for "DECISION: MOVE/FIRE direction" pattern
    const decisionMatch = finalResponse.match(/DECISION:\s*(MOVE|FIRE)\s+(up|down|left|right)/i);
    if (decisionMatch) {
      action = decisionMatch[1].toLowerCase() as 'move' | 'fire';
      direction = decisionMatch[2].toLowerCase() as 'up' | 'down' | 'left' | 'right';
      console.log(`🎯 Parsed decision: ${action} ${direction}`);
    } else {
      // Fallback parsing
      console.log('⚠️ No DECISION pattern found, using fallback parsing');
      if (finalResponse.toLowerCase().includes('fire')) {
        action = 'fire';
      }

      if (finalResponse.toLowerCase().includes('up')) direction = 'up';
      else if (finalResponse.toLowerCase().includes('down')) direction = 'down';
      else if (finalResponse.toLowerCase().includes('left')) direction = 'left';
      else if (finalResponse.toLowerCase().includes('right')) direction = 'right';

      // If still no direction, use simple logic based on tool calls
      if (direction === 'none' && result.toolCalls.length > 0) {
        // Find the closest battery and move toward it
        const distanceCall = result.toolCalls.find(tc => tc.name === 'calculate_distances');
        if (distanceCall && distanceCall.result && distanceCall.result.length > 0) {
          const closestTarget = distanceCall.result[0].target;
          const rowDiff = closestTarget.row - input.botRow;
          const colDiff = closestTarget.col - input.botCol;

          if (Math.abs(rowDiff) > Math.abs(colDiff)) {
            direction = rowDiff > 0 ? 'down' : 'up';
          } else {
            direction = colDiff > 0 ? 'right' : 'left';
          }
          reason = `Moving ${direction} toward target at (${closestTarget.row}, ${closestTarget.col})`;
          console.log(`🔧 Used tool result for direction: ${direction}`);
        }
      }
    }

    return {
      action,
      direction,
      reason,
      toolCallsLog,
      finalAnalysis: `Used ${result.toolCalls.length} tool calls to analyze the situation`
    };

  } catch (error) {
    console.error('❌ Tool-based mission failed:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('Error details:', {
      message: errorMessage,
      stack: error instanceof Error ? error.stack : 'No stack trace',
      name: error instanceof Error ? error.name : 'Unknown error'
    });

    // Fallback to simple logic
    return {
      action: 'move',
      direction: 'down',
      reason: `Tool-based analysis failed: ${errorMessage}. Using fallback logic.`,
      toolCallsLog: 'Error: ' + String(error),
      finalAnalysis: 'Fallback used due to error'
    };
  }
}
