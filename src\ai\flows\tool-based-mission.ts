'use server';

/**
 * @fileOverview Tool-based mission planning using OpenAI function calling
 * This demonstrates the tool calling approach vs structured output
 */

// Simple reliable mission execution without complex tool calling

export type ToolBasedMissionInput = {
  grid: string[][];
  botRow: number;
  botCol: number;
  objective: string;
  turtleLocations: Array<{row: number, col: number}>;
  batteryLocations: Array<{row: number, col: number}>;
  currentBatteries: number; // Add current battery count
};

export type ToolBasedMissionOutput = {
  action: 'move' | 'fire';
  direction: 'up' | 'down' | 'left' | 'right' | 'none';
  reason: string;
  toolCallsLog: string;
  finalAnalysis: string;
};

// Simple reliable mission execution



export async function executeToolBasedMission(input: ToolBasedMissionInput): Promise<ToolBasedMissionOutput> {
  console.log('🔧 Executing tool-based mission logic');
  console.log(`Bot at (${input.botRow}, ${input.botCol}), Objective: ${input.objective}`);

  // SIMPLE RELIABLE APPROACH - No complex tool calling for now
  // Just use direct calculation to avoid game resets

  if (input.objective.includes('batteries') && input.batteryLocations.length > 0) {
    // Find closest battery
    const distances = input.batteryLocations.map(battery => ({
      battery,
      distance: Math.abs(battery.row - input.botRow) + Math.abs(battery.col - input.botCol)
    }));

    distances.sort((a, b) => a.distance - b.distance);
    const closestBattery = distances[0].battery;

    // Calculate direction to move
    const rowDiff = closestBattery.row - input.botRow;
    const colDiff = closestBattery.col - input.botCol;

    let direction: 'up' | 'down' | 'left' | 'right';
    if (Math.abs(rowDiff) > Math.abs(colDiff)) {
      direction = rowDiff > 0 ? 'down' : 'up';
    } else {
      direction = colDiff > 0 ? 'right' : 'left';
    }

    return {
      action: 'move',
      direction,
      reason: `Moving ${direction} toward closest battery at (${closestBattery.row}, ${closestBattery.col})`,
      toolCallsLog: `Direct calculation: closest battery distance ${distances[0].distance}`,
      finalAnalysis: 'Using simple reliable calculation'
    };
  }

  if (input.objective.includes('turtles') && input.turtleLocations.length > 0) {
    console.log(`🐢 TURTLE ELIMINATION MISSION: ${input.turtleLocations.length} turtles remaining`);
    console.log(`🤖 Bot position: (${input.botRow}, ${input.botCol})`);
    console.log(`🔋 Current batteries: ${input.currentBatteries}`);
    console.log(`🎯 Turtle locations:`, input.turtleLocations.map(t => `(${t.row},${t.col})`).join(', '));

    // CHECK BATTERY REQUIREMENT FIRST!
    const turtlesRemaining = input.turtleLocations.length;
    const batteriesNeeded = turtlesRemaining; // Need 1 battery per turtle

    if (input.currentBatteries <= 0) {
      console.log(`⚠️ NO BATTERIES! Need to collect batteries before firing laser`);

      if (input.batteryLocations.length === 0) {
        return {
          action: 'move',
          direction: 'up',
          reason: `❌ No batteries available to fire laser and no batteries on map! Cannot eliminate turtles.`,
          toolCallsLog: `ERROR: No batteries available`,
          finalAnalysis: 'Mission impossible - no batteries to fire laser'
        };
      }

      // Find closest battery and collect it first
      const batteryDistances = input.batteryLocations.map(battery => ({
        battery,
        distance: Math.abs(battery.row - input.botRow) + Math.abs(battery.col - input.botCol)
      }));

      batteryDistances.sort((a, b) => a.distance - b.distance);
      const closestBattery = batteryDistances[0].battery;

      // Calculate direction to move toward battery
      const rowDiff = closestBattery.row - input.botRow;
      const colDiff = closestBattery.col - input.botCol;

      let direction: 'up' | 'down' | 'left' | 'right';
      if (Math.abs(rowDiff) > Math.abs(colDiff)) {
        direction = rowDiff > 0 ? 'down' : 'up';
      } else {
        direction = colDiff > 0 ? 'right' : 'left';
      }

      console.log(`🔋 COLLECTING BATTERY: Moving ${direction} toward battery at (${closestBattery.row}, ${closestBattery.col})`);
      return {
        action: 'move',
        direction,
        reason: `🔋 Need batteries to fire laser! Moving ${direction} toward battery at (${closestBattery.row}, ${closestBattery.col})`,
        toolCallsLog: `BATTERY COLLECTION: distance ${batteryDistances[0].distance} to battery`,
        finalAnalysis: `Collecting battery first - need ammo to eliminate ${turtlesRemaining} turtles`
      };
    }

    // SMART BATTERY MANAGEMENT: If we have fewer batteries than turtles, collect more batteries first
    if (input.currentBatteries < turtlesRemaining && input.batteryLocations.length > 0) {
      console.log(`⚠️ BATTERY SHORTAGE: Have ${input.currentBatteries} batteries but need ${batteriesNeeded} for ${turtlesRemaining} turtles`);

      // Find closest battery and collect it first
      const batteryDistances = input.batteryLocations.map(battery => ({
        battery,
        distance: Math.abs(battery.row - input.botRow) + Math.abs(battery.col - input.botCol)
      }));

      batteryDistances.sort((a, b) => a.distance - b.distance);
      const closestBattery = batteryDistances[0].battery;

      // Calculate direction to move toward battery
      const rowDiff = closestBattery.row - input.botRow;
      const colDiff = closestBattery.col - input.botCol;

      let direction: 'up' | 'down' | 'left' | 'right';
      if (Math.abs(rowDiff) > Math.abs(colDiff)) {
        direction = rowDiff > 0 ? 'down' : 'up';
      } else {
        direction = colDiff > 0 ? 'right' : 'left';
      }

      console.log(`🔋 STRATEGIC BATTERY COLLECTION: Moving ${direction} toward battery at (${closestBattery.row}, ${closestBattery.col})`);
      return {
        action: 'move',
        direction,
        reason: `🔋 Strategic battery collection! Have ${input.currentBatteries} but need ${batteriesNeeded} batteries. Moving ${direction} toward battery.`,
        toolCallsLog: `STRATEGIC BATTERY: distance ${batteryDistances[0].distance} to battery`,
        finalAnalysis: `Collecting batteries strategically - need ${batteriesNeeded - input.currentBatteries} more batteries`
      };
    }

    // We have batteries, proceed with turtle elimination
    console.log(`✅ Have ${input.currentBatteries} batteries - proceeding with turtle elimination`);

    // Find closest turtle
    const distances = input.turtleLocations.map(turtle => ({
      turtle,
      distance: Math.abs(turtle.row - input.botRow) + Math.abs(turtle.col - input.botCol)
    }));

    distances.sort((a, b) => a.distance - b.distance);
    const closestTurtle = distances[0].turtle;
    console.log(`🎯 Targeting closest turtle at (${closestTurtle.row}, ${closestTurtle.col}), distance: ${distances[0].distance}`);

    // Check if we can fire at it (same row or column)
    const sameRow = closestTurtle.row === input.botRow;
    const sameCol = closestTurtle.col === input.botCol;

    console.log(`🔍 Line of sight check: sameRow=${sameRow}, sameCol=${sameCol}`);

    if (sameRow || sameCol) {
      // FINAL BATTERY CHECK: Don't fire if we'd run out of batteries with more turtles remaining
      const batteriesAfterShot = input.currentBatteries - 1;
      const turtlesAfterShot = turtlesRemaining - 1;

      if (batteriesAfterShot < turtlesAfterShot && input.batteryLocations.length > 0) {
        console.log(`⚠️ BATTERY CONSERVATION: Would have ${batteriesAfterShot} batteries but ${turtlesAfterShot} turtles remaining. Collecting more batteries first.`);

        // Find closest battery instead of firing
        const batteryDistances = input.batteryLocations.map(battery => ({
          battery,
          distance: Math.abs(battery.row - input.botRow) + Math.abs(battery.col - input.botCol)
        }));

        batteryDistances.sort((a, b) => a.distance - b.distance);
        const closestBattery = batteryDistances[0].battery;

        const rowDiff = closestBattery.row - input.botRow;
        const colDiff = closestBattery.col - input.botCol;

        let direction: 'up' | 'down' | 'left' | 'right';
        if (Math.abs(rowDiff) > Math.abs(colDiff)) {
          direction = rowDiff > 0 ? 'down' : 'up';
        } else {
          direction = colDiff > 0 ? 'right' : 'left';
        }

        return {
          action: 'move',
          direction,
          reason: `🔋 Battery conservation! Could fire now but need more batteries for remaining turtles. Moving ${direction} to collect battery.`,
          toolCallsLog: `BATTERY CONSERVATION: ${batteriesAfterShot} batteries after shot < ${turtlesAfterShot} turtles remaining`,
          finalAnalysis: `Conserving batteries - collecting more before continuing elimination`
        };
      }

      // Safe to fire - we have enough batteries
      let direction: 'up' | 'down' | 'left' | 'right';
      if (sameRow) {
        direction = closestTurtle.col > input.botCol ? 'right' : 'left';
      } else {
        direction = closestTurtle.row > input.botRow ? 'down' : 'up';
      }

      console.log(`🔥 FIRING ${direction} at turtle at (${closestTurtle.row}, ${closestTurtle.col})!`);
      return {
        action: 'fire',
        direction,
        reason: `🔥 Firing ${direction} at turtle at (${closestTurtle.row}, ${closestTurtle.col}). Will have ${batteriesAfterShot} batteries left for ${turtlesAfterShot} turtles.`,
        toolCallsLog: `FIRE: turtle in line of sight at distance ${distances[0].distance}, ${input.currentBatteries} batteries available`,
        finalAnalysis: `Firing at turtle - ${turtlesAfterShot} turtles remaining after this shot`
      };
    } else {
      // Move to align with turtle - prioritize getting to same row/column
      const rowDiff = closestTurtle.row - input.botRow;
      const colDiff = closestTurtle.col - input.botCol;

      let direction: 'up' | 'down' | 'left' | 'right';
      let strategy = '';

      // Strategy: Move to same row or column first
      if (rowDiff === 0) {
        // Already same row, move horizontally
        direction = colDiff > 0 ? 'right' : 'left';
        strategy = 'aligning horizontally on same row';
      } else if (colDiff === 0) {
        // Already same column, move vertically
        direction = rowDiff > 0 ? 'down' : 'up';
        strategy = 'aligning vertically on same column';
      } else {
        // Need to align - choose shortest path to same row or column
        if (Math.abs(rowDiff) <= Math.abs(colDiff)) {
          // Move to same row first
          direction = rowDiff > 0 ? 'down' : 'up';
          strategy = 'moving to same row for line of sight';
        } else {
          // Move to same column first
          direction = colDiff > 0 ? 'right' : 'left';
          strategy = 'moving to same column for line of sight';
        }
      }

      console.log(`🚶 MOVING ${direction} to align with turtle (${strategy})`);
      return {
        action: 'move',
        direction,
        reason: `🚶 Moving ${direction} to align with turtle at (${closestTurtle.row}, ${closestTurtle.col}) - ${strategy}`,
        toolCallsLog: `MOVE: ${strategy}, distance ${distances[0].distance}, ${input.currentBatteries} batteries ready`,
        finalAnalysis: `Moving to get line of sight on turtle - ${input.turtleLocations.length} turtles remaining`
      };
    }
  }

  // Fallback
  return {
    action: 'move',
    direction: 'up',
    reason: 'No targets found, moving up',
    toolCallsLog: 'No targets available',
    finalAnalysis: 'Fallback movement'
  };

}
