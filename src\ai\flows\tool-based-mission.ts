'use server';

/**
 * @fileOverview Tool-based mission planning using OpenAI function calling
 * This demonstrates the tool calling approach vs structured output
 */

// Simple reliable mission execution without complex tool calling

export type ToolBasedMissionInput = {
  grid: string[][];
  botRow: number;
  botCol: number;
  objective: string;
  turtleLocations: Array<{row: number, col: number}>;
  batteryLocations: Array<{row: number, col: number}>;
  currentBatteries: number; // Add current battery count
};

export type ToolBasedMissionOutput = {
  action: 'move' | 'fire';
  direction: 'up' | 'down' | 'left' | 'right' | 'none';
  reason: string;
  toolCallsLog: string;
  finalAnalysis: string;
  workflowSteps: Array<{
    timestamp: string;
    toolName: string;
    args: any;
    result: any;
    reasoning: string;
  }>;
};

// Tool definitions for LLM function calling
const missionTools = [
  {
    name: 'calculate_distances',
    description: 'Calculate Manhattan distances from bot to multiple targets',
    parameters: {
      type: 'object',
      properties: {
        botRow: { type: 'number', description: 'Current bot row position' },
        botCol: { type: 'number', description: 'Current bot column position' },
        targets: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              row: { type: 'number' },
              col: { type: 'number' }
            }
          },
          description: 'Array of target positions to calculate distances to'
        }
      },
      required: ['botRow', 'botCol', 'targets']
    },
    handler: async (args: { botRow: number; botCol: number; targets: Array<{row: number, col: number}> }) => {
      return args.targets.map(target => ({
        target,
        distance: Math.abs(target.row - args.botRow) + Math.abs(target.col - args.botCol)
      })).sort((a, b) => a.distance - b.distance);
    }
  },
  {
    name: 'find_best_target',
    description: 'Find the best target considering distance, line of sight, and obstacles',
    parameters: {
      type: 'object',
      properties: {
        botRow: { type: 'number', description: 'Current bot row position' },
        botCol: { type: 'number', description: 'Current bot column position' },
        targets: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              row: { type: 'number' },
              col: { type: 'number' }
            }
          },
          description: 'Array of potential targets'
        },
        obstacles: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              row: { type: 'number' },
              col: { type: 'number' }
            }
          },
          description: 'Array of obstacle positions'
        }
      },
      required: ['botRow', 'botCol', 'targets', 'obstacles']
    },
    handler: async (args: { botRow: number; botCol: number; targets: Array<{row: number, col: number}>; obstacles: Array<{row: number, col: number}> }) => {
      let bestTarget = args.targets[0];
      let bestScore = -1;
      let reason = '';

      for (const target of args.targets) {
        const distance = Math.abs(target.row - args.botRow) + Math.abs(target.col - args.botCol);

        // Prefer targets in same row/column (easier path)
        const sameRowCol = (target.row === args.botRow || target.col === args.botCol) ? 10 : 0;

        // Check for obstacles in path
        const hasObstacles = args.obstacles.some(obs =>
          (obs.row === args.botRow && obs.col === target.col) ||
          (obs.col === args.botCol && obs.row === target.row)
        );
        const obstaclesPenalty = hasObstacles ? -5 : 0;

        const score = (100 / distance) + sameRowCol + obstaclesPenalty;

        if (score > bestScore) {
          bestScore = score;
          bestTarget = target;
          reason = `Target at (${target.row},${target.col}) chosen: distance=${distance}, same row/col bonus=${sameRowCol}, obstacles penalty=${obstaclesPenalty}`;
        }
      }

      return { target: bestTarget, score: bestScore, reason };
    }
  },
  {
    name: 'get_next_move',
    description: 'Calculate the next move direction to reach a target',
    parameters: {
      type: 'object',
      properties: {
        botRow: { type: 'number', description: 'Current bot row position' },
        botCol: { type: 'number', description: 'Current bot column position' },
        targetRow: { type: 'number', description: 'Target row position' },
        targetCol: { type: 'number', description: 'Target column position' }
      },
      required: ['botRow', 'botCol', 'targetRow', 'targetCol']
    },
    handler: async (args: { botRow: number; botCol: number; targetRow: number; targetCol: number }) => {
      const rowDiff = args.targetRow - args.botRow;
      const colDiff = args.targetCol - args.botCol;

      if (Math.abs(rowDiff) > Math.abs(colDiff)) {
        if (rowDiff > 0) {
          return { direction: 'down', reason: `Moving down to reach target row ${args.targetRow}` };
        } else {
          return { direction: 'up', reason: `Moving up to reach target row ${args.targetRow}` };
        }
      } else {
        if (colDiff > 0) {
          return { direction: 'right', reason: `Moving right to reach target column ${args.targetCol}` };
        } else {
          return { direction: 'left', reason: `Moving left to reach target column ${args.targetCol}` };
        }
      }
    }
  },
  {
    name: 'can_fire_at_target',
    description: 'Check if bot can fire at a target (same row or column)',
    parameters: {
      type: 'object',
      properties: {
        botRow: { type: 'number', description: 'Current bot row position' },
        botCol: { type: 'number', description: 'Current bot column position' },
        targetRow: { type: 'number', description: 'Target row position' },
        targetCol: { type: 'number', description: 'Target column position' }
      },
      required: ['botRow', 'botCol', 'targetRow', 'targetCol']
    },
    handler: async (args: { botRow: number; botCol: number; targetRow: number; targetCol: number }) => {
      if (args.botRow === args.targetRow) {
        const direction = args.targetCol > args.botCol ? 'right' : 'left';
        return {
          canFire: true,
          direction,
          reason: `Can fire ${direction} at target in same row`
        };
      }

      if (args.botCol === args.targetCol) {
        const direction = args.targetRow > args.botRow ? 'down' : 'up';
        return {
          canFire: true,
          direction,
          reason: `Can fire ${direction} at target in same column`
        };
      }

      return {
        canFire: false,
        reason: 'Target not in line of sight - need to move first'
      };
    }
  },
  {
    name: 'check_battery_requirements',
    description: 'Check if bot has enough batteries for the mission',
    parameters: {
      type: 'object',
      properties: {
        currentBatteries: { type: 'number', description: 'Current number of batteries' },
        turtlesRemaining: { type: 'number', description: 'Number of turtles to eliminate' },
        batteryLocations: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              row: { type: 'number' },
              col: { type: 'number' }
            }
          },
          description: 'Available battery locations on map'
        }
      },
      required: ['currentBatteries', 'turtlesRemaining', 'batteryLocations']
    },
    handler: async (args: { currentBatteries: number; turtlesRemaining: number; batteryLocations: Array<{row: number, col: number}> }) => {
      const batteriesNeeded = args.turtlesRemaining;
      const hasSufficient = args.currentBatteries >= batteriesNeeded;
      const canCollectMore = args.batteryLocations.length > 0;

      return {
        hasSufficient,
        batteriesNeeded,
        canCollectMore,
        recommendation: hasSufficient ? 'proceed' : (canCollectMore ? 'collect_batteries' : 'mission_impossible')
      };
    }
  }
];

// Simple reliable mission execution



// Helper function to create workflow steps
function createWorkflowStep(toolName: string, args: any, result: any, reasoning: string) {
  return {
    timestamp: new Date().toISOString(),
    toolName,
    args,
    result,
    reasoning
  };
}

// NEW: Real LLM tool calling approach
export async function executeToolBasedMissionWithLLM(input: ToolBasedMissionInput): Promise<ToolBasedMissionOutput> {
  console.log('🤖 Using REAL LLM tool calling approach');

  const { createToolCompletion } = await import('../openai-client');

  const systemPrompt = `You are an AI controlling a bot in a grid-based game. Your goal is to help the bot complete missions efficiently.

CURRENT SITUATION:
- Bot position: (${input.botRow}, ${input.botCol})
- Objective: ${input.objective}
- Current batteries: ${input.currentBatteries}
- Turtles remaining: ${input.turtleLocations.length}
- Batteries available: ${input.batteryLocations.length}

GAME RULES:
1. Bot can move up/down/left/right
2. Bot can fire laser up/down/left/right (costs 1 battery)
3. Laser only hits targets in same row or column
4. Need 1 battery per turtle to eliminate all turtles

MISSION STRATEGY:
1. For turtle elimination: Check battery requirements first
2. If insufficient batteries: collect batteries before firing
3. Move to get line of sight (same row/column) before firing
4. For battery collection: move toward closest battery

Use the available tools to analyze the situation and determine the best next action.
Return your final decision as either:
- {"action": "move", "direction": "up/down/left/right", "reasoning": "why"}
- {"action": "fire", "direction": "up/down/left/right", "reasoning": "why"}`;

  try {
    const result = await createToolCompletion(
      [{ role: 'user', content: systemPrompt }],
      missionTools,
      'gpt-4o-mini', // model
      5 // maxIterations
    );

    console.log('🤖 LLM tool calling result:', result);

    // Convert tool calls to workflow steps
    const workflowSteps = result.toolCalls.map(tc => createWorkflowStep(
      tc.name,
      tc.arguments,
      tc.result,
      `LLM called ${tc.name} to analyze the situation`
    ));

    // Parse the final response to extract action and direction
    let action: 'move' | 'fire' = 'move';
    let direction: 'up' | 'down' | 'left' | 'right' = 'up';
    let reasoning = result.finalResponse || 'No reasoning provided';

    try {
      if (result.finalResponse) {
        const parsed = JSON.parse(result.finalResponse);
        if (parsed.action && parsed.direction) {
          action = parsed.action;
          direction = parsed.direction;
          reasoning = parsed.reasoning || reasoning;
        }
      }
    } catch (e) {
      // If parsing fails, try to extract from text
      if (result.finalResponse?.includes('fire')) {
        action = 'fire';
      }
      if (result.finalResponse?.includes('up')) direction = 'up';
      else if (result.finalResponse?.includes('down')) direction = 'down';
      else if (result.finalResponse?.includes('left')) direction = 'left';
      else if (result.finalResponse?.includes('right')) direction = 'right';
    }

    return {
      action,
      direction,
      reason: reasoning,
      toolCallsLog: `LLM made ${result.toolCalls.length} tool calls: ${result.toolCalls.map(tc => tc.name).join(', ')}`,
      finalAnalysis: result.finalResponse || 'LLM analysis complete',
      workflowSteps
    };

  } catch (error) {
    console.error('❌ LLM tool calling failed:', error);

    // Fallback to simple logic
    return {
      action: 'move',
      direction: 'up',
      reason: 'LLM tool calling failed, using fallback',
      toolCallsLog: `Error: ${error}`,
      finalAnalysis: 'Fallback to simple logic due to LLM error',
      workflowSteps: [createWorkflowStep('error', { error: String(error) }, null, 'LLM tool calling failed')]
    };
  }
}

export async function executeToolBasedMission(input: ToolBasedMissionInput): Promise<ToolBasedMissionOutput> {
  console.log('🔧 Executing tool-based mission logic');
  console.log(`Bot at (${input.botRow}, ${input.botCol}), Objective: ${input.objective}`);

  const workflowSteps: Array<{
    timestamp: string;
    toolName: string;
    args: any;
    result: any;
    reasoning: string;
  }> = [];

  // SIMPLE RELIABLE APPROACH - No complex tool calling for now
  // Just use direct calculation to avoid game resets

  if (input.objective.includes('batteries') && input.batteryLocations.length > 0) {
    // Find closest battery
    const calculateArgs = {
      botRow: input.botRow,
      botCol: input.botCol,
      targets: input.batteryLocations
    };

    const distances = input.batteryLocations.map(battery => ({
      battery,
      distance: Math.abs(battery.row - input.botRow) + Math.abs(battery.col - input.botCol)
    }));

    distances.sort((a, b) => a.distance - b.distance);
    const closestBattery = distances[0].battery;

    workflowSteps.push(createWorkflowStep(
      'calculate_distances',
      calculateArgs,
      distances.slice(0, 3), // Show top 3 closest
      `Found ${distances.length} batteries, closest is ${distances[0].distance} steps away`
    ));

    // Calculate direction to move
    const moveArgs = {
      botRow: input.botRow,
      botCol: input.botCol,
      targetRow: closestBattery.row,
      targetCol: closestBattery.col
    };

    const rowDiff = closestBattery.row - input.botRow;
    const colDiff = closestBattery.col - input.botCol;

    let direction: 'up' | 'down' | 'left' | 'right';
    if (Math.abs(rowDiff) > Math.abs(colDiff)) {
      direction = rowDiff > 0 ? 'down' : 'up';
    } else {
      direction = colDiff > 0 ? 'right' : 'left';
    }

    const moveResult = { direction, reason: `Moving ${direction} toward target` };
    workflowSteps.push(createWorkflowStep(
      'get_next_move',
      moveArgs,
      moveResult,
      'Calculated optimal direction to move toward closest battery'
    ));

    return {
      action: 'move',
      direction,
      reason: `Moving ${direction} toward closest battery at (${closestBattery.row}, ${closestBattery.col})`,
      toolCallsLog: `Direct calculation: closest battery distance ${distances[0].distance}`,
      finalAnalysis: 'Using simple reliable calculation',
      workflowSteps
    };
  }

  if (input.objective.includes('turtles') && input.turtleLocations.length > 0) {
    console.log(`🐢 TURTLE ELIMINATION MISSION: ${input.turtleLocations.length} turtles remaining`);
    console.log(`🤖 Bot position: (${input.botRow}, ${input.botCol})`);
    console.log(`🔋 Current batteries: ${input.currentBatteries}`);
    console.log(`🎯 Turtle locations:`, input.turtleLocations.map(t => `(${t.row},${t.col})`).join(', '));

    // CHECK BATTERY REQUIREMENT FIRST!
    const turtlesRemaining = input.turtleLocations.length;
    const batteriesNeeded = turtlesRemaining; // Need 1 battery per turtle

    if (input.currentBatteries <= 0) {
      console.log(`⚠️ NO BATTERIES! Need to collect batteries before firing laser`);

      if (input.batteryLocations.length === 0) {
        workflowSteps.push(createWorkflowStep(
          'check_battery_availability',
          { batteryLocations: input.batteryLocations },
          { available: false },
          'No batteries available on map - mission impossible'
        ));

        return {
          action: 'move',
          direction: 'up',
          reason: `❌ No batteries available to fire laser and no batteries on map! Cannot eliminate turtles.`,
          toolCallsLog: `ERROR: No batteries available`,
          finalAnalysis: 'Mission impossible - no batteries to fire laser',
          workflowSteps
        };
      }

      // Find closest battery and collect it first
      const batteryDistances = input.batteryLocations.map(battery => ({
        battery,
        distance: Math.abs(battery.row - input.botRow) + Math.abs(battery.col - input.botCol)
      }));

      batteryDistances.sort((a, b) => a.distance - b.distance);
      const closestBattery = batteryDistances[0].battery;

      // Calculate direction to move toward battery
      const rowDiff = closestBattery.row - input.botRow;
      const colDiff = closestBattery.col - input.botCol;

      let direction: 'up' | 'down' | 'left' | 'right';
      if (Math.abs(rowDiff) > Math.abs(colDiff)) {
        direction = rowDiff > 0 ? 'down' : 'up';
      } else {
        direction = colDiff > 0 ? 'right' : 'left';
      }

      console.log(`🔋 COLLECTING BATTERY: Moving ${direction} toward battery at (${closestBattery.row}, ${closestBattery.col})`);
      return {
        action: 'move',
        direction,
        reason: `🔋 Need batteries to fire laser! Moving ${direction} toward battery at (${closestBattery.row}, ${closestBattery.col})`,
        toolCallsLog: `BATTERY COLLECTION: distance ${batteryDistances[0].distance} to battery`,
        finalAnalysis: `Collecting battery first - need ammo to eliminate ${turtlesRemaining} turtles`,
        workflowSteps: []
      };
    }

    // SMART BATTERY MANAGEMENT: If we have fewer batteries than turtles, collect more batteries first
    if (input.currentBatteries < turtlesRemaining && input.batteryLocations.length > 0) {
      console.log(`⚠️ BATTERY SHORTAGE: Have ${input.currentBatteries} batteries but need ${batteriesNeeded} for ${turtlesRemaining} turtles`);

      // Find closest battery and collect it first
      const batteryDistances = input.batteryLocations.map(battery => ({
        battery,
        distance: Math.abs(battery.row - input.botRow) + Math.abs(battery.col - input.botCol)
      }));

      batteryDistances.sort((a, b) => a.distance - b.distance);
      const closestBattery = batteryDistances[0].battery;

      // Calculate direction to move toward battery
      const rowDiff = closestBattery.row - input.botRow;
      const colDiff = closestBattery.col - input.botCol;

      let direction: 'up' | 'down' | 'left' | 'right';
      if (Math.abs(rowDiff) > Math.abs(colDiff)) {
        direction = rowDiff > 0 ? 'down' : 'up';
      } else {
        direction = colDiff > 0 ? 'right' : 'left';
      }

      console.log(`🔋 STRATEGIC BATTERY COLLECTION: Moving ${direction} toward battery at (${closestBattery.row}, ${closestBattery.col})`);
      return {
        action: 'move',
        direction,
        reason: `🔋 Strategic battery collection! Have ${input.currentBatteries} but need ${batteriesNeeded} batteries. Moving ${direction} toward battery.`,
        toolCallsLog: `STRATEGIC BATTERY: distance ${batteryDistances[0].distance} to battery`,
        finalAnalysis: `Collecting batteries strategically - need ${batteriesNeeded - input.currentBatteries} more batteries`,
        workflowSteps: []
      };
    }

    // We have batteries, proceed with turtle elimination
    console.log(`✅ Have ${input.currentBatteries} batteries - proceeding with turtle elimination`);

    workflowSteps.push(createWorkflowStep(
      'check_battery_status',
      { currentBatteries: input.currentBatteries, turtlesRemaining },
      { sufficient: true, canProceed: true },
      `Have ${input.currentBatteries} batteries for ${turtlesRemaining} turtles - sufficient to proceed`
    ));

    // Find closest turtle
    const distances = input.turtleLocations.map(turtle => ({
      turtle,
      distance: Math.abs(turtle.row - input.botRow) + Math.abs(turtle.col - input.botCol)
    }));

    distances.sort((a, b) => a.distance - b.distance);
    const closestTurtle = distances[0].turtle;
    console.log(`🎯 Targeting closest turtle at (${closestTurtle.row}, ${closestTurtle.col}), distance: ${distances[0].distance}`);

    workflowSteps.push(createWorkflowStep(
      'calculate_distances',
      { botRow: input.botRow, botCol: input.botCol, targets: input.turtleLocations },
      distances.slice(0, 3), // Show top 3 closest
      `Found ${distances.length} turtles, targeting closest at distance ${distances[0].distance}`
    ));

    // Check if we can fire at it (same row or column)
    const sameRow = closestTurtle.row === input.botRow;
    const sameCol = closestTurtle.col === input.botCol;

    console.log(`🔍 Line of sight check: sameRow=${sameRow}, sameCol=${sameCol}`);

    workflowSteps.push(createWorkflowStep(
      'can_fire_at_target',
      { botRow: input.botRow, botCol: input.botCol, targetRow: closestTurtle.row, targetCol: closestTurtle.col },
      { canFire: sameRow || sameCol, sameRow, sameCol },
      sameRow || sameCol ? 'Target in line of sight - can fire!' : 'Need to move to get line of sight'
    ));

    if (sameRow || sameCol) {
      // FINAL BATTERY CHECK: Don't fire if we'd run out of batteries with more turtles remaining
      const batteriesAfterShot = input.currentBatteries - 1;
      const turtlesAfterShot = turtlesRemaining - 1;

      if (batteriesAfterShot < turtlesAfterShot && input.batteryLocations.length > 0) {
        console.log(`⚠️ BATTERY CONSERVATION: Would have ${batteriesAfterShot} batteries but ${turtlesAfterShot} turtles remaining. Collecting more batteries first.`);

        // Find closest battery instead of firing
        const batteryDistances = input.batteryLocations.map(battery => ({
          battery,
          distance: Math.abs(battery.row - input.botRow) + Math.abs(battery.col - input.botCol)
        }));

        batteryDistances.sort((a, b) => a.distance - b.distance);
        const closestBattery = batteryDistances[0].battery;

        const rowDiff = closestBattery.row - input.botRow;
        const colDiff = closestBattery.col - input.botCol;

        let direction: 'up' | 'down' | 'left' | 'right';
        if (Math.abs(rowDiff) > Math.abs(colDiff)) {
          direction = rowDiff > 0 ? 'down' : 'up';
        } else {
          direction = colDiff > 0 ? 'right' : 'left';
        }

        return {
          action: 'move',
          direction,
          reason: `🔋 Battery conservation! Could fire now but need more batteries for remaining turtles. Moving ${direction} to collect battery.`,
          toolCallsLog: `BATTERY CONSERVATION: ${batteriesAfterShot} batteries after shot < ${turtlesAfterShot} turtles remaining`,
          finalAnalysis: `Conserving batteries - collecting more before continuing elimination`,
          workflowSteps: []
        };
      }

      // Safe to fire - we have enough batteries
      let direction: 'up' | 'down' | 'left' | 'right';
      if (sameRow) {
        direction = closestTurtle.col > input.botCol ? 'right' : 'left';
      } else {
        direction = closestTurtle.row > input.botRow ? 'down' : 'up';
      }

      console.log(`🔥 FIRING ${direction} at turtle at (${closestTurtle.row}, ${closestTurtle.col})!`);
      return {
        action: 'fire',
        direction,
        reason: `🔥 Firing ${direction} at turtle at (${closestTurtle.row}, ${closestTurtle.col}). Will have ${batteriesAfterShot} batteries left for ${turtlesAfterShot} turtles.`,
        toolCallsLog: `FIRE: turtle in line of sight at distance ${distances[0].distance}, ${input.currentBatteries} batteries available`,
        finalAnalysis: `Firing at turtle - ${turtlesAfterShot} turtles remaining after this shot`,
        workflowSteps: []
      };
    } else {
      // Move to align with turtle - prioritize getting to same row/column
      const rowDiff = closestTurtle.row - input.botRow;
      const colDiff = closestTurtle.col - input.botCol;

      let direction: 'up' | 'down' | 'left' | 'right';
      let strategy = '';

      // Strategy: Move to same row or column first
      if (rowDiff === 0) {
        // Already same row, move horizontally
        direction = colDiff > 0 ? 'right' : 'left';
        strategy = 'aligning horizontally on same row';
      } else if (colDiff === 0) {
        // Already same column, move vertically
        direction = rowDiff > 0 ? 'down' : 'up';
        strategy = 'aligning vertically on same column';
      } else {
        // Need to align - choose shortest path to same row or column
        if (Math.abs(rowDiff) <= Math.abs(colDiff)) {
          // Move to same row first
          direction = rowDiff > 0 ? 'down' : 'up';
          strategy = 'moving to same row for line of sight';
        } else {
          // Move to same column first
          direction = colDiff > 0 ? 'right' : 'left';
          strategy = 'moving to same column for line of sight';
        }
      }

      console.log(`🚶 MOVING ${direction} to align with turtle (${strategy})`);
      return {
        action: 'move',
        direction,
        reason: `🚶 Moving ${direction} to align with turtle at (${closestTurtle.row}, ${closestTurtle.col}) - ${strategy}`,
        toolCallsLog: `MOVE: ${strategy}, distance ${distances[0].distance}, ${input.currentBatteries} batteries ready`,
        finalAnalysis: `Moving to get line of sight on turtle - ${input.turtleLocations.length} turtles remaining`,
        workflowSteps: []
      };
    }
  }

  // Fallback
  return {
    action: 'move',
    direction: 'up',
    reason: 'No targets found, moving up',
    toolCallsLog: 'No targets available',
    finalAnalysis: 'Fallback movement',
    workflowSteps: []
  };

}
